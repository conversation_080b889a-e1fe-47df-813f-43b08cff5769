<template>
  <el-date-picker
    :value="value"
    :type="type"
    :value-format="valueFormat"
    v-bind="$attrs"
    v-on="$listeners"
    @input="handleInput"
  ></el-date-picker>
</template>

<script>
export default {
  props: {
    value: {
      type: [String, null]
    },
    valueFormat: {
      type: String,
      default: 'yyyy-MM-dd HH:mm:ss'
    },
    type: {
      type: String,
      default: 'date'
    }
  },
  methods: {
    async handleInput(val) {
      this.$emit('input', val)

      if (val) return
      const parent = this.$parent
      if (parent?.prop) {
        parent.elForm.validateField(parent.prop)
      }
    }
  }
}
</script>
