<template>
  <div>
  <el-dialog
    title="查看交付"
    :visible.sync="dialogVisible"
    width="50%"
    :before-close="handleClose"
    class="view-delivery-dialog"
  >
    <div v-loading="loading" class="dialog-content">
      <div v-if="deliveryData.length > 0" class="delivery-list">
        <div
          v-for="(item, index) in deliveryData"
          :key="index"
          class="delivery-item"
        >
          <el-card style="margin: 8px;">
            <h4 class="delivery-title">{{ getDeliveryTitle(index) }}</h4>
            <p class="info-item">交付时间: {{ item.deliverTime | formatDateTime }}</p>
            <p class="info-item">交付描述: {{ item.deliveryDesc || '-' }}</p>
            <p class="info-item">上传成果:</p>
            <div v-if="item.fileId && item.fileId.length > 0" class="file-grid">
              <div
                v-for="(fileId, fileIndex) in item.fileId"
                :key="fileIndex"
                class="file-preview-item"
              >
                <!-- 图片预览 -->
                <div v-if="isImageFile(fileId) && !fileLoadErrors[fileId]" class="image-preview">
                  <div class="preview-image-container">
                    <img
                      :src="getFilePreviewUrl(fileId)"
                      :alt="getFileName(fileId) || `附件${fileIndex + 1}`"
                      class="preview-image"
                      @error="handleImageError(fileId)"
                    />
                  </div>
                  <div class="file-info">
                    <span class="file-name" :title="getFileName(fileId) || `附件${fileIndex + 1}`">{{ getFileName(fileId) || `附件${fileIndex + 1}` }}</span>
                  </div>
                  <div class="file-overlay">
                    <div class="file-actions">
                      <i class="el-icon-zoom-in" @click="previewImage(fileId)" title="预览"></i>
                      <i class="el-icon-download" @click="downloadFile(fileId)" title="下载"></i>
                    </div>
                  </div>
                </div>

                <!-- 视频预览 -->
                <div v-else-if="isVideoFile(fileId)" class="video-preview">
                  <div class="video-icon-container">
                    <i class="el-icon-video-play file-type-icon" style="color: #E6A23C"></i>
                  </div>
                  <div class="file-info">
                    <span class="file-name" :title="getFileName(fileId) || `附件${fileIndex + 1}`">{{ getFileName(fileId) || `附件${fileIndex + 1}` }}</span>
                  </div>
                  <div class="file-overlay">
                    <div class="file-actions">
                      <i class="el-icon-video-play" @click="previewVideo(fileId)" title="播放视频"></i>
                      <i class="el-icon-download" @click="downloadFile(fileId)" title="下载"></i>
                    </div>
                  </div>
                </div>

                <!-- 非图片、非视频文件预览 -->
                <div v-else class="file-preview">
                  <div class="file-icon-container">
                    <i
                      :class="getFileIcon(fileId).icon"
                      class="file-type-icon"
                      :style="{ color: getFileIcon(fileId).color }"
                    ></i>
                  </div>
                  <div class="file-info">
                    <span class="file-name" :title="getFileName(fileId) || `附件${fileIndex + 1}`">{{ getFileName(fileId) || `附件${fileIndex + 1}` }}</span>
                  </div>
                  <div class="file-overlay">
                    <div class="file-actions">
                      <i class="el-icon-download" @click="downloadFile(fileId)" title="下载"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <span v-else>无附件</span>
            
            <!-- 企业审核信息 -->
            <div v-if="item.auditTime" class="audit-info">
              <p class="info-item">企业审核时间: {{ item.auditTime | formatDateTime }}</p>
              <p class="info-item">审核结果: <span :class="item.auditResult ? 'audit-success' : 'audit-fail'">{{ item.auditResult ? '成功' : '失败' }}</span></p>
              <p v-if="!item.auditResult && item.auditFailReason" class="info-item">失败原因: {{ item.auditFailReason }}</p>
            </div>
          </el-card>
        </div>
      </div>
      <div v-else class="empty-data">
      <div class="empty-content">
          <i class="el-icon-folder-opened empty-icon"></i>
          <div>暂无交付记录</div>
        </div>  
        </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关 闭</el-button>
    </span>
  </el-dialog>
  
    <!-- 图片预览弹窗 -->
    <el-dialog
      :visible.sync="previewVisible"
      :show-close="true"
      :modal="true"
      :close-on-click-modal="true"
      width="auto"
      custom-class="image-preview-dialog"
    >
      <img
        v-if="previewImageUrl"
        :src="previewImageUrl"
        style="max-width: 100%; max-height: 70vh"
      />
    </el-dialog>

    <!-- 视频预览弹窗 -->
    <el-dialog
      :visible.sync="videoPreviewVisible"
      :show-close="true"
      :modal="true"
      :close-on-click-modal="true"
      width="auto"
      custom-class="video-preview-dialog"
    >
      <video
        v-if="previewVideoUrl"
        :src="previewVideoUrl"
        controls
        style="width: 100%; max-height: 60vh"
        preload="metadata"
      >
        您的浏览器不支持视频播放
      </video>
    </el-dialog>
  </div>
</template>

<script>
import handleError from '../../../../helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'

const client = makeClient()

export default {

  props: {
    visible: {
      type: Boolean,
      default: false
    },
    taskLaborId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      loading: false,
      deliveryData: [],
      fileLoadErrors: {},
      previewVisible: false,
      previewImageUrl: '',
      // 存储文件信息：fileId -> { name, type }
      fileInfoMap: {},
      // 视频预览相关
      videoPreviewVisible: false,
      previewVideoUrl: ''
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.fetchDeliveryData()
      }
    }
  },
  methods: {
    async fetchDeliveryData() {
      if (!this.taskLaborId) return
      this.loading = true
      try {
        const [err, res] = await client.getDelivery(this.taskLaborId)
        if (err) {
          handleError(err)
          this.deliveryData = []
          return
        }
        this.deliveryData = res.data || []
        
        // 调用描述文件方法获取文件信息
        await this.describeFiles()
      } catch (error) {
        handleError(error)
        this.deliveryData = []
      } finally {
        this.loading = false
      }
    },
    handleClose() {
      this.deliveryData = []
      this.fileInfoMap = {}
      this.dialogVisible = false
    },
    getDeliveryTitle(index) {
      const numerals = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
      if (index === 0) {
        return '首次'
      }
      if (index > 0 && index < 10) {
        return `第${numerals[index]}次`
      }
      return `第${index + 1}次`
    },
    handleImageError(fileId) {
      this.$set(this.fileLoadErrors, fileId, true)
    },
    getFilePreviewUrl(fileId) {
      return `${window.env?.apiPath}/api/public/previewFile/${fileId}`
    },
    downloadFile(fileId) {
      window.location.href = `${window.env?.apiPath}/api/public/downloadFile/${fileId}`
    },
    previewImage(fileId) {
      this.previewImageUrl = this.getFilePreviewUrl(fileId)
      this.previewVisible = true
    },

    previewVideo(fileId) {
      this.previewVideoUrl = this.getFilePreviewUrl(fileId)
      this.videoPreviewVisible = true
    },

    // 获取文件名称
    getFileName(fileId) {
      const fileInfo = this.fileInfoMap[fileId]
      return fileInfo ? fileInfo.name : null
    },

    // 判断是否为图片文件
    isImageFile(fileId) {
      const fileName = this.getFileName(fileId)
      if (!fileName) return false
      
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
      const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
      return imageExtensions.includes(extension)
    },

    // 判断是否为视频文件
    isVideoFile(fileId) {
      const fileName = this.getFileName(fileId)
      if (!fileName) return false
      
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v']
      const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
      return videoExtensions.includes(extension)
    },

    getFileIcon(fileId) {
      const fileName = this.getFileName(fileId)
      if (!fileName) {
        return { icon: 'el-icon-document', color: '#5470c6' }
      }
      
      const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
      
      // 根据文件扩展名返回不同的图标
      if (['.pdf'].includes(extension)) {
        return { icon: 'el-icon-document', color: '#5470c6' }
      } else if (['.doc', '.docx'].includes(extension)) {
        return { icon: 'el-icon-document', color: '#5470c6' }
      } else if (['.xls', '.xlsx'].includes(extension)) {
        return { icon: 'el-icon-s-grid', color: '#5470c6' }
      } else if (['.zip', '.rar', '.7z'].includes(extension)) {
        return { icon: 'el-icon-folder-opened', color: '#5470c6' }
      } else {
        return { icon: 'el-icon-document-copy', color: '#5470c6' }
      }
    },

    // 描述文件方法
    async describeFiles() {
      if (!this.deliveryData || this.deliveryData.length === 0) {
        return
      }

      console.log('开始调用描述文件方法...')
      
      // 遍历所有交付记录
      for (let deliveryIndex = 0; deliveryIndex < this.deliveryData.length; deliveryIndex++) {
        const delivery = this.deliveryData[deliveryIndex]
        
        if (delivery.fileId && delivery.fileId.length > 0) {
          console.log(`第${deliveryIndex + 1}次交付包含 ${delivery.fileId.length} 个文件`)
          
          // 遍历每个文件ID
          for (let fileIndex = 0; fileIndex < delivery.fileId.length; fileIndex++) {
            const fileId = delivery.fileId[fileIndex]
            console.log(`准备查询文件ID: ${fileId}`)
            
            try {
              // 调用描述文件接口
              const [err, response] = await this.callDescribeFile(fileId)
              
              if (err) {
                console.error(`查询文件 ${fileId} 失败:`, err)
              } else {
                console.log(`文件ID: ${fileId}`, response)
                
                // 存储文件信息到 fileInfoMap 中
                if (response && response.data && response.data.name) {
                  this.$set(this.fileInfoMap, fileId, {
                    name: response.data.name,
                    type: this.getFileTypeFromName(response.data.name)
                  })
                  console.log(`文件 ${fileId} 信息已存储:`, this.fileInfoMap[fileId])
                }
              }
            } catch (error) {
              console.error(`查询文件 ${fileId} 异常:`, error)
            }
          }
        } else {
          console.log(`第${deliveryIndex + 1}次交付无文件`)
        }
      }
    },

    // 调用描述文件接口
    async callDescribeFile(fileId) {
      const requestBody = {
        id: fileId
      }

      return await client.describeFile({
        body: requestBody
      })
    },

    // 根据文件名获取文件类型
    getFileTypeFromName(fileName) {
      if (!fileName) return 'unknown'
      
      const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
      
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v']
      
      if (imageExtensions.includes(extension)) {
        return 'image'
      } else if (videoExtensions.includes(extension)) {
        return 'video'
      } else {
        return 'document'
      }
    }
  },
  filters: {
    formatDateTime(value) {
      if (!value) return ''
      return new Date(value).toLocaleString('zh-CN', { hour12: false })
    }
  }
}
</script>

<style scoped>
.view-delivery-dialog .dialog-content {
  min-height: 300px;
  max-height: 60vh;
  overflow-y: auto;
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px; /* 确保有一个最小高度来实现垂直居中 */
  color: #909399;
}

.empty-icon {
  font-size: 64px; /* 增大图标大小 */
  margin-bottom: 20px;
}

.empty-text {
  font-size: 14px;
}

.delivery-title {
  background-color: #409eff;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 10px;
}

.el-card h4 {
  margin-top: 0;
}

.file-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
}

.file-preview-item {
  position: relative;
  width: 160px;
  height: 140px;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.file-preview-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #409eff;
}

.file-preview-item:hover .file-overlay {
  opacity: 1;
}

.image-preview, .file-preview, .video-preview {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-image-container, .file-icon-container, .video-icon-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  padding: 8px;
  overflow: hidden;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.file-type-icon {
  font-size: 48px;
}

.file-info {
  padding: 8px 12px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  height: 36px;
  box-sizing: border-box;
}

.file-name {
  font-size: 12px;
  color: #606266;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;
}

.file-actions {
  display: flex;
  gap: 16px;
}

.file-actions i {
  cursor: pointer;
  font-size: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-actions i:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* 预览弹窗样式 */
::v-deep .image-preview-dialog {
  text-align: center;
  width: auto !important;
}

::v-deep .video-preview-dialog {
  .el-dialog__body {
    padding: 20px;
    text-align: center;
  }
}

.info-item {
  color: #606266;
  font-weight: normal;
}

.delivery-item + .delivery-item {
  margin-top: 20px;
}

.audit-info {
  padding-top: 10px;
}

.audit-success {
  color: #67c23a;
  font-weight: bold;
}

.audit-fail {
  color: #f56c6c;
  font-weight: bold;
}
</style>
