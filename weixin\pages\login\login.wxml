<view style="padding: 20px; background-color: #fff; min-height: 100vh; box-sizing: border-box;">
  <!-- Logo -->
  <view style="text-align: center; padding: 40px 0;">
    <image src="{{logoUrl}}" style="width: 100px; height: 100px;"></image>
  </view>
  <view style="display: flex; align-items: center; background-color: #f2f2f2; border-radius: 25px; padding: 0 20px; height: 50px; margin-bottom: 20px;">
    <mp-icon icon="cellphone" color="#888" size="{{20}}"></mp-icon>
    <input type="number" placeholder="请输入手机号码" value="{{phone}}" bindinput="onPhoneInput" maxlength="11" style="flex: 1; border: none; background-color: transparent; margin-left: 10px; height: 100%;" />
  </view>
  <view wx:if="{{ loginType === 'password' }}" style="display: flex; align-items: center; background-color: #f2f2f2; border-radius: 25px; padding: 0 20px; height: 50px; margin-bottom: 20px;">
    <mp-icon icon="lock" color="#888" size="{{20}}" />
    <input type="password" placeholder="请输入登录密码" value="{{password}}" bindinput="onPasswordInput" maxlength="20" style="flex: 1; border: none; background-color: transparent; margin-left: 10px; height: 100%;" />
  </view>
  <view style="display: flex; align-items: center; background-color: #f2f2f2; border-radius: 25px; padding: 0 20px; height: 50px; margin-bottom: 20px;">
    <mp-icon icon="info" color="#888" size="{{20}}" />
    <input type="text" placeholder="请输入图形验证码" value="{{captcha.answer}}" bindinput="onCaptchaInput" maxlength="4" style="flex: 1; border: none; background-color: transparent; margin-left: 10px; height: 100%;" />
    <image bindtap="refreshCaptcha" src="{{captcha.url}}" style="width: 80px; height: 30px; margin-right: 17px; border-radius: 2px;"></image>
  </view>

  <view wx:if="{{ loginType === 'smsCode' }}" style="display: flex; align-items: center; background-color: #f2f2f2; border-radius: 25px; padding: 0 20px; height: 50px;">
    <mp-icon icon="lock" color="#888" size="{{20}}"></mp-icon>
    <input type="number" placeholder="请输入验证码" value="{{code}}" bindinput="onCodeInput" maxlength="6" style="flex: 1; border: none; background-color: transparent; margin-left: 10px; height: 100%;" />
    <view style="width: 1px; height: 20px; background-color: #e0e0e0; margin: 0 8px;"></view>
    <text bindtap="getVerificationCode" style="color: {{countdown > 0 ? '#999' : 'var(--primary-color)'}}; font-size: 14px; position: relative;top:-10px; white-space: nowrap;">
      {{countdown > 0 ? countdown + 's 后重发' : '获取验证码'}}
    </text>
  </view>

  <view wx:if="{{ loginType === 'forgetPassword' }}" style="display: flex; align-items: center; background-color: #f2f2f2; border-radius: 25px; padding: 0 20px; height: 50px;">
    <mp-icon icon="lock" color="#888" size="{{20}}"></mp-icon>
    <input type="number" placeholder="请输入验证码" value="{{code}}" bindinput="onCodeInput" maxlength="6" style="flex: 1; border: none; background-color: transparent; margin-left: 10px; height: 100%;" />
    <view style="width: 1px; height: 20px; background-color: #e0e0e0; margin: 0 8px;"></view>
    <text bindtap="getForgetPasswordCode" style="color: {{countdown > 0 ? '#999' : 'var(--primary-color)'}}; font-size: 14px; position: relative;top:-10px; white-space: nowrap;">
      {{countdown > 0 ? countdown + 's 后重发' : '获取验证码'}}
    </text>
  </view>
  <view wx:if="{{ loginType === 'forgetPassword' }}" style="display: flex; align-items: center; background-color: #f2f2f2; border-radius: 25px; padding: 0 20px; height: 50px; margin: 20px 0;">
    <mp-icon icon="lock" color="#888" size="{{20}}" />
    <input type="password" placeholder="6-20位数字、字母和符号" value="{{newPassword}}" bindinput="onNewPasswordInput" maxlength="20" style="flex: 1; border: none; background-color: transparent; margin-left: 10px; height: 100%;" />
  </view>
  <view wx:if="{{ loginType === 'forgetPassword' }}" style="display: flex; align-items: center; background-color: #f2f2f2; border-radius: 25px; padding: 0 20px; height: 50px; margin-bottom: 20px;">
    <mp-icon icon="lock" color="#888" size="{{20}}" />
    <input type="password" placeholder="请再次输入密码" value="{{confirmPassword}}" bindinput="onConfirmPasswordInput" maxlength="20" style="flex: 1; border: none; background-color: transparent; margin-left: 10px; height: 100%;" />
  </view>
  <!-- 登录方式切换 -->
  <view style="margin: 10px 0;">
    <view wx:if="{{ loginType === 'smsCode' }}" class="type-item" style="color: var(--primary-color);">
      <text bindtap="handleLoginTypeClick" data-type="password">密码登录</text>
    </view>
    <view wx:if="{{ loginType === 'password' }}" style="display: flex; justify-content: space-between;">
      <view class="type-item" style="color: var(--primary-color);">
        <text bindtap="handleLoginTypeClick" data-type="smsCode">验证码登录</text>
      </view>
      <view class="type-item" style="color: var(--primary-color);">
        <text bindtap="handleForgetPassword">忘记密码</text>
      </view>
    </view>
  </view>
  <!-- Login Button -->
  <button wx:if="{{ loginType !== 'forgetPassword' }}" style="width: 100%; height: 50px; border-radius: 25px; background-color: var(--primary-color); color: white; border: none; font-size: 16px; display: flex; align-items: center; justify-content: center;" bindtap="login">
    登 录
  </button>
  <button wx:else style="width: 100%; height: 50px; border-radius: 25px; background-color: var(--primary-color); color: white; border: none; font-size: 16px; display: flex; align-items: center; justify-content: center;" bindtap="handleConfirm">
    确 定
  </button>
</view>