<template>
  <div class="filter-tags">
    <div style="padding: 10px 18px; background: #fff">
      <h3
        style="
          font-size: 15px;
          font-weight: 600;
          color: #262935;
          margin: 20px 0;
        "
      >
        筛选标签
      </h3>
      <div
        style="
          display: flex;
          flex-wrap: wrap;
          align-content: flex-start;
          gap: 16px;
          height: calc(100vh - 160px);
          overflow: scroll;
        "
      >
        <div
          style="
            flex: 0 0 calc(33.3333% - 11px);
            height: 83px;
            box-sizing: border-box;
            background: #ecf6ff;
            text-align: center;
            border-radius: 4px;
          "
          v-for="(item, index) in tagOptions"
          :key="index"
        >
          <div
            :class="[item.taskTagId === activeTagId ? 'curr' : 'item-block']"
            @click="handleSelected(item.taskTagId)"
          >
            <img
              :src="previewImg(item.fileId)"
              style="
                width: 30px;
                height: 30px;
                object-fit: contain;
                position: absolute;
                bottom: 42px;
                right: 50%;
                margin-right: -14px;
              "
              alt=""
            />
            <div class="tags-name">{{ item.taskTagName }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="btns">
      <Button style="color: #0082ff; margin-right: 20px" @click="handleReset"
        >重置</Button
      >
      <Button style="color: #FFFFFF;; background: #4f71ff" @click="handleConfirm"
        >确认</Button
      >
    </div>
  </div>
</template>
<script>
import { Grid, GridItem, Button } from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Grid,
    GridItem,
    Button
  },
  data() {
    return {
      tagOptions: [],
      activeTagId: ''
    }
  },
  created() {
    this.activeTagId = this.$route.query.taskTagId || ''
    this.queryTaskTag()
  },
  methods: {
    async queryTaskTag() {
      const [err, r] = await client.queryPublicTaskTag()
      if (err) return handleError(err)
      this.tagOptions = r.data
    },
    previewImg(fileId) {
      return `${window.env?.apiPath}/api/public/previewFile/${fileId}`
    },
    handleSelected(tagId) {
      this.activeTagId = tagId
    },
    handleReset() {
      this.activeTagId = ''
      this.handleConfirm()
    },
    handleConfirm() {
      const taskTagName = this.tagOptions.find(item => item.taskTagId === this.activeTagId)?.taskTagName
      this.$router.push({
        path: '/homePage',
        query: {
          taskTagId: this.activeTagId,
          taskTagName: taskTagName
        }
      })
    }
  }
}
</script>
<style scoped>
.tags-name {
  width: 80px;
  height: 35px;
  text-align: center;
  font-size: 13px;
  font-weight: 400;
  color: rgba(38, 41, 53, 1);
  position: absolute;
  bottom: 0px;
  right: 50%;
  margin-right: -40px;
}
.curr {
  height: 83px;
  color: rgba(38, 41, 53, 1);
  border-radius: 4px;
  border: 1px solid #0082ff;
  background: #ecf6ff;
  font-size: 13px;
  font-weight: 400;
  position: relative;
}
.item-block {
  height: 83px;
  color: rgba(38, 41, 53, 1);
  font-size: 13px;
  font-weight: 400;
  position: relative;
}
.btns {
  position: fixed;
  width: 100%;
  box-sizing: border-box;
  bottom: 0;
  padding: 20px;
  background: #ffffff;
  box-shadow: 0 -2px 4px 0 #edf1f8;
}
.btns button {
  border: 1px solid #0082ff !important;
  border-radius: 20px;
  width: 150px;
  height: 40px;
}
</style>
