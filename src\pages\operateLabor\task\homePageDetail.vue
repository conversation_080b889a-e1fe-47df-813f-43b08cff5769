<template>
  <div style="padding: 0 10px">
    <NavBar left-text="返回" left-arrow @click-left="onClickLeft" />

    <div style="height: calc(100vh - 140px); overflow: auto">
      <Cell
        style="
          background-image: linear-gradient(179deg, #e9edff 3%, #f9faff 100%);
          border-radius: 4px;
        "
      >
        <div
          v-if="info.laborTaskStatus"
          class="status"
          :style="{
            background: getBgColor(info.laborTaskStatus)
          }"
        >
          {{ info.laborTaskStatus }}
        </div>
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <h2>{{ info.taskName }}</h2>
        </div>
        <div
          style="
            display: flex;
            flex-direction: column;
            color: #828b9b;
            font-size: 14px;
          "
        >
          <div style="color: #4f71ff; font-size: 16px; font-weight: 600">
            {{ formatterPrice(info.taskAmount) }}
            <span style="font-size: 10px" v-if="info.taskAmount !== '面议'"
              >元</span
            >
          </div>
          <span
            >任务时间：{{ formatter(info.taskStartTime) }} -
            {{ formatter(info.taskEndTime) }}</span
          >
        </div>
      </Cell>
      <Cell>
        <h2>用工企业</h2>
        <span class="name">{{ info.enterpriseInfoName }}</span>
      </Cell>
      <Cell>
        <h2>派工企业</h2>
        <span class="name">{{ info.corporationName }}</span>
      </Cell>
      <Cell>
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <h2>人员要求</h2>
          <span style="font-weight: 400; font-size: 14px; color: #4e5769"
            >招收人数：{{ info.requiredCount }}</span
          >
        </div>
        <span class="name"
          >{{ info.requiredGender }} | {{ info.requiredAge }} |
          {{ info.requiredEducation }}</span
        >
      </Cell>
      <Cell>
        <h2>任务地点</h2>
        <span class="name">{{ info.taskLocation }}</span>
      </Cell>
      <Cell>
        <h2>任务描述</h2>
        <span class="name">{{ info.taskDescription || '-' }}</span>
      </Cell>
    </div>
    <div
      style="
        width: 100vw;
        height: 40px;
        position: fixed;
        bottom: 0;
        left: 0;
        padding: 20px 0px;
        box-shadow: 0 -5px 8px 0 #282e3a0d;
      "
    >
      <div
        v-if="currentType === 'recommend'"
        class="btn"
        style="margin: 0 20px"
        @click="handleApply"
      >
        申请任务
      </div>
      <div v-else style="display: flex; justify-content: center">
        <div
          class="btn"
          style="
            width: 163px;
            background: #fff;
            color: #2681d7;
            border: 1px solid #3b7eb3;
            box-sizing: border-box;
            margin-right: 9px;
          "
          @click="handleRefuse"
        >
          拒绝任务
        </div>
        <div
          class="btn"
          style="width: 163px; box-sizing: border-box"
          @click="handleReceive"
        >
          接受任务
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { NavBar, Cell, Icon } from 'vant'
import handleError from '../../../helpers/handleErrorH5'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    NavBar,
    Cell,
    Icon
  },
  data() {
    return {
      info: {},
      profile: null,
      currentType: ''
    }
  },
  async created() {
    this.currentType = this.$route.query.type
    await this.init()
    await this.getProfile()
  },
  methods: {
    async init() {
      const id = this.$route.query.id
      const [err, r] = await client.personalTaskGet(id)
      if (err) return handleError(err)
      this.info = r.data
    },
    async getProfile() {
      const [err, r] = await client.personalLaborProfile()
      if (err) return handleError(err)
      this.profile = r.data
    },
    getBgColor(status) {
      const colorMap = {
        待确认: '#FF9A01',
        进行中: '#4F71FF',
        已完成: '#07BB06',
        已拒绝: '#F63939',
        待审核: '#FF9A01',
        待接受: '#FF9A01'
      }
      return colorMap[status]
    },
    onClickLeft() {
      this.$router.replace('/homePage')
    },
    formatterPrice(value) {
      if (!value) return
      if (value.includes('-')) {
        return value.split('-')[0] + ' ' + '-' + ' ' + value.split('-')[1]
      } else if (value === '面议') {
        return '面议'
      } else {
        return value
      }
    },
    formatter(time) {
      if (!time) return ''
      return time.slice(0, 10)
    },
    formatterCount(index) {
      if (index == 0) {
        return '首次'
      }
      return `第${index + 1}次`
    },
    formatterImg(id) {
      return `${window.env.apiPath}/api/public/previewFile/${id}`
    },
    async handleApply() {
      if (!this.profile.authStatus) {
        this.$router.push({
          path: '/ocr',
          query: {
            source: 'wx-homePage-detail',
            id: this.$route.query.id,
            type: this.currentType
          }
        })
        return
      }
      const [err, r] = await client.personalClaim({
        body: {
          taskId: this.$route.query.id
        }
      })
      if (err) return handleError(err)

      if (!r.data.isSigned) {
        this.$router.push({
          path: '/showContract',
          query: {
            source: 'wx-homePage-detail',
            taskId: this.$route.query.id,
            protocolId: r.data.protocolId,
            archiveId: r.data.archiveId,
            currentType: this.currentType
          }
        })
      } else {
        this.$toast('认领成功')
        this.$router.replace('/homePage')
      }
    },
    async handleReceive() {
      if (!this.profile.authStatus) {
        this.$router.push({
          path: '/ocr',
          query: {
            source: 'wx-homePage-detail',
            id: this.$route.query.id,
            type: this.currentType
          }
        })
        return
      }
      const [err, r] = await client.personalAccept({
        body: {
          taskId: this.$route.query.id,
          auditResult: true
        }
      })
      if (err) return handleError(err)
      if (!r.data.isSigned) {
        this.$router.push({
          path: '/showContract',
          query: {
            source: 'wx-homePage-detail',
            taskId: this.$route.query.id,
            protocolId: r.data.protocolId,
            archiveId: r.data.archiveId,
            currentType: this.currentType
          }
        })
      } else {
        this.$toast('接受成功')
        this.$router.replace('/homePage')
      }
    },
    async handleRefuse() {
      const [err, r] = await client.personalAccept({
        body: {
          taskId: this.$route.query.id,
          auditResult: false
        }
      })
      if (err) return handleError(err)
      this.$toast('拒绝成功')
      this.$router.go(-1)
    }
  }
}
</script>
<style scoped>
.type-bar {
  height: 36px;
  display: flex;
  justify-content: space-around;
  font-size: 15px;
  margin: 10px 0;
}
.type-bar span {
  text-align: center;
  margin-right: 23px;
  font-weight: 400;
  padding: 6px 0;
}
.active {
  border-bottom: 2px solid #0082ff;
  color: #0082ff;
  font-weight: 500;
  padding: 6px 0;
}
.status {
  position: absolute;
  top: 0;
  right: 0;
  width: 64px;
  height: 24px;
  border-radius: 0 4px 0 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 10% 100%, 2% 20%);
  font-weight: 600;
  font-size: 12px;
  color: #ffffff;
  line-height: 16px;
}
h2 {
  font-size: 16px;
  font-weight: 600;
}
.name {
  color: #828b9b;
}
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #4f71ff;
  color: #fff;
  border-radius: 20px;
  padding: 10px;
}
::v-deep .van-cell {
  padding: 0;
}
::v-deep .van-cell__value {
  padding: 10px 16px;
}
</style>
