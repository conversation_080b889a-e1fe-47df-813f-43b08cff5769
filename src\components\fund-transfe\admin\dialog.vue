<template>
  <div>
    <el-dialog
      :title="title"
      :visible="value"
      @update:visible="onVisible"
      :width="width"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <slot></slot>
      <span slot="footer" class="dialog-footer">
        <el-button class="button" @click="close">取 消</el-button>
        <el-button
          class="button"
          type="primary"
          :loading="loading"
          @click="onConfirm"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
  <script>
export default {
  components: {},
  props: {
    title: {
      type: String,
      default: '标题'
    },
    value: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '560px'
    },
    confirm: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false
    }
  },

  methods: {
    onVisible(value) {
      this.$emit('input', value)
    },
    close() {
      this.onVisible(false)
      this.$emit('close')
    },
    async onConfirm() {
      this.loading = true
      this.confirm && (await this.confirm())
      this.loading = false
    }
  }
}
</script>
  <style scoped>
::v-deep .el-dialog__header {
  padding: 16px 24px;
  box-sizing: border-box;
  border: none;
}

::v-deep .el-dialog__body {
  padding: 24px;
  border: 1px solid #f8f8f8;
}
::v-deep .el-dialog__title {
  color: #1e2228;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}
::v-deep .el-form-item__label {
  line-height: 22px;
  padding-bottom: 0px;
  margin-bottom: 8px;
}
::v-deep .el-upload-dragger {
  height: 32px;
  border: none;
}
::v-deep .el-dialog__footer {
  padding: 10px 24px 10px 0;
}
</style>
  