<template>
  <div
    class="setting"
    style="
      height: 100vh;
      background: #f4f4f4;
      padding: 20px 16px;
      box-sizing: border-box;
    "
  >
    <NavBar style="margin-bottom: 1px;" left-text="返回" left-arrow @click-left="handleBack" />
    <Cell @click="goLoginPassword">
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div style="font-weight: 400; font-size: 14px; color: #24262a">
          登录密码
        </div>
        <Icon name="arrow" />
      </div>
    </Cell>
    <div class="signOut" @click="logout">退出登录</div>
  </div>
</template>
<script>
import { Cell, Icon, NavBar } from 'vant'
import { Dialog } from 'vant'
import { removeToken } from 'kit/helpers/token'
import initWechatSDK from 'kit/helpers/initWechatSDK'
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Dialog,
    Cell,
    Icon,
    NavBar
  },
  data() {
    return {
      changeCompanyStatus: false,
      haveBorder: false,
      title: '设置',
      settingList: [
        {
          title: '登录密码',
          url: '/loginPassword',
          rightItem: '',
          show: true
        }
        // {
        //   title: '安全邮箱',
        //   url: '/pages/settingEmail/main',
        //   rightItem: '',
        //   show: true
        // }
      ],
      ruleForm: {
        cellPhone: ''
      }
    }
  },
  mounted() {
    initWechatSDK()
      .then(success => {
        if (success) {
          console.log('qwer521')
        }
      })
      .catch(err => {
        console.error('初始化失败', err)
      })
  },

  methods: {
    goLoginPassword() {
      this.$router.push({
        path: '/loginPassword',
        query: {
          cellphone: this.$route.query.cellphone
        }
      })
    },
    logout() {
      Dialog.confirm({
        title: '提示',
        message: '确定退出吗?'
      }).then(async () => {
        removeToken()
        wx.miniProgram.reLaunch({
          url: '/pages/login/login'
        })
      })
    },
    handleBack() {
      this.$router.push('/mine')
    }
  }
}
</script>
<style>
page {
  background-color: #f5f5f5;
}
</style>
<style scoped>
::v-deep .nav-bar-box {
  background: #f5f5f5;
}
::v-deep .nav-bar-wrap {
  background: #f5f5f5;
}
::v-deep .nav-bar {
  background: #f5f5f5;
}
.signOut {
  height: 48px;
  line-height: 48px;
  text-align: center;
  border: 0.5px solid #a8acba;
  border-radius: 6px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 16px;
  color: #777c94;
  margin: 0 16px;
  position: fixed;
  bottom: 50px;
  left: 0;
  right: 0;
}
</style>
