import 'kit/assets/themes/element.ui.css'
import 'kit/assets/iconfont/iconfont.css'
// import './table-scrollbar.css'
import { setTheme } from 'kit/assets/themes'
import App from './app.vue'
import Vue from 'vue'
import ElementUI from 'element-ui'
import VueRouter from 'vue-router'
import routes from './routes'
import { getToken } from 'kit/helpers/token'

Vue.use(ElementUI)
Vue.use(VueRouter)

const theme = window.env.theme || 'default'
setTheme(theme)

const router = new VueRouter({
  mode: 'history',
  base: '/platform/',
  routes
})

// 不需要登录验证的页面
const noAuthPages = ['/login', '/loginWithCaptcha', '/register', '/findPassword']

router.beforeEach((to, from, next) => {
  document.title = to.meta.title

  // 检查是否需要登录验证
  if (!noAuthPages.includes(to.path)) {
    const token = getToken()
    if (!token) {
      // 没有token，跳转到登录页
      next('/login')
      return
    }
  }

  next()
})

const vStore = null //为了避免类型检查
new Vue({
  el: '#app',
  router,
  vStore,
  render: h => h(App)
}).$mount()
