<template>
  <div
    style="
      min-height: 100vh;
      background: #f8f8f8;
      padding: 20px 16px 0;
      box-sizing: border-box;
    "
  >
    <div style="position: relative; margin-bottom: 10px">
      <Search
        v-model="value"
        placeholder="搜索任务名称"
        @search="onSearch"
      />
      <div 
        style="
          position: absolute;
          right: 40px;
          top: 50%;
          transform: translateY(-50%);
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        "
        @click="onSearch"
      >
        <img 
          src="../../../../assets/images/weixin/base-find-search.png" 
          style="width: 16px; height: 16px"
          alt="搜索"
        />
      </div>
    </div>
    <div v-if="dataList.length">
      <div
        class="task"
        v-for="(item, index) in dataList"
        :key="index"
        @click="handleClick(item)"
      >
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <div style="display: flex; flex-direction: column">
            <span
              style="
                width: 200px;
                font-size: 16px;
                font-weight: 600;
                color: #262935;
                margin-top: 7px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
                text-overflow: ellipsis;
              "
              >{{ item.taskName }}</span
            >
            <span
              style="
                width: 200px;
                white-space: pre-wrap;
                font-size: 13px;
                color: #71788f;
                margin-top: 10px;
              "
              >{{ item.enterpriseName || '-' }}</span
            >
          </div>
          <div style="font-size: 20px; font-weight: 600; color: #0082ff">
            {{ formatterPrice(item.taskAmount) }}
          </div>
        </div>
        <div
          style="
            display: inline-block;
            margin-top: 10px;
            padding: 4px 8px;
            background: #e2e8ff;
            border-radius: 4px;
          "
        >
          <span style="font-size: 12px; color: #4f71ff">{{
            item.taskTagName
          }}</span>
        </div>
        <div style="height: 1px; background: #f0f2f7; margin-top: 20px"></div>
        <div
          style="
            margin-top: 10px;
            display: flex;
            justify-content: space-between;
          "
        >
          <span style="font-size: 13px; color: #71788f; margin-top: 8px"
            >{{ formatterTime(item.taskStartTime) }} -
            {{ formatterTime(item.taskEndTime) }}</span
          >
          <div class="claim-btn">立即认领</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { Search } from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Search
  },
  data() {
    return {
      value: '',
      dataList: []
    }
  },
  computed: {
    currType() {
      return this.$route.query.currType
    }
  },
  methods: {
    formatterPrice(value) {
      if (value.includes('-')) {
        return value.split('-')[0] + '+元'
      } else if (value === '面议') {
        return '面议'
      } else {
        return value + '元'
      }
    },
    formatterTime(time) {
      if (!time) return ''
      return time.slice(0, 10)
    },
    async onSearch() {
      const [err, r] = await client.personalQueryTask({
        body: {
          offset: 0,
          limit: 10000,
          withTotal: true,
          withDisabled: true,
          withDeleted: true,
          filters: {
            taskName: this.value,
            taskStatus: 'DOING',
            taskTagId: '',
            taskVisible: this.currType === 'recommend' ? 'ALL' : '',
            claimType: this.currType === 'recommend' ? '' : 'ASSIGNED_CLAIM'
          }
        }
      })
      if (err) return handleError(err)
      this.dataList = r.data.list
    },
    handleClick(item) {
      this.$router.push({
        path: '/home/<USER>',
        query: {
          id: item.taskId,
          type: this.currType
        }
      })
    }
  }
}
</script>
<style scoped>
.task {
  padding: 15px;
  background: #fff;
  margin-bottom: 10px;
  position: relative;
  border-radius: 4px;
}
.claim-btn {
  width: 90px;
  height: 30px;
  background: #0082ff;
  border-radius: 4px;
  font-size: 15px;
  font-weight: 400;
  color: #ffffff;
  line-height: 30px;
  text-align: center;
}
</style>
