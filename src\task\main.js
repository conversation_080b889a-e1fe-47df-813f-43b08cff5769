import App from './app.vue'
import Vue from 'vue'
import VueRouter from 'vue-router'
import routes from './routes'
import 'vant/lib/index.css'
import { setToken } from 'kit/helpers/token'

Vue.use(VueRouter)

const router = new VueRouter({
  mode: 'history',
  base: '/task/',
  routes
})
const url = new URLSearchParams(window.location.search)
const token = url.get('token')
if (token && !localStorage.getItem('token')) {
  console.log('token===', token)
  setToken(token)
}
router.beforeEach((to, from, next) => {
  document.title = to.meta.title
  next()
})

const vStore = null //为了避免类型检查
new Vue({
  el: '#app',
  router,
  vStore,
  render: h => h(App)
}).$mount()
