<template>
  <div style="height: 100vh; padding: 20px 16px">
    <div style="color: #e14c46">
      *<span style="color: #272935">添加描述:</span>
    </div>
    <Field
      v-model="deliveryDesc"
      style="
        height: 95px;
        margin: 10px 0;
        border-radius: 5px;
        border: 1px solid #e8e8e8;
      "
      type="textarea"
      maxlength="50"
      placeholder="请输入任务完成情况"
      show-word-limit
    />
    <div v-if="!fileList.length">
      <div style="color: #e14c46">
        *<span style="color: #272935">上传成果（拍照）:</span>
      </div>
      <Uploader
        style="margin: 10px 0"
        v-model="photoFileList"
        :max-count="1"
        :max-size="1024 * 1024 * 50"
        accept="image/*"
      ></Uploader>
    </div>
    <div v-if="!photoFileList.length">
      <div style="color: #e14c46">
        *<span style="color: #272935">上传成果（选择文件）:</span>
      </div>
      <Uploader
        style="margin: 10px 0"
        v-model="fileList"
        :max-count="1"
        :max-size="1024 * 1024 * 50"
        accept="*"
      ></Uploader>
    </div>
    <div style="color: #8d93a6">
      格式支持：jpg，jpeg，png，pdf，xls，xlsx，MP4，AVI，WMV，RMVB，mov，zip，rar
    </div>
    <div style="color: #8d93a6">支持大小20M</div>
    <div
      style="
        width: 100vw;
        height: 40px;
        position: fixed;
        bottom: 0;
        padding: 20px 0;
      "
    >
      <Button class="btn" @click="handleConfirm" :loading="loading"
        >确 认</Button
      >
    </div>
  </div>
</template>
<script>
import { Field, Uploader, Button } from 'vant'
import handleError from '../../../helpers/handleErrorH5'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Field,
    Uploader,
    Button
  },
  data() {
    return {
      deliveryDesc: '',
      photoFileList: [],
      fileList: [],
      fileId: [],
      loading: false
    }
  },
  methods: {
    async handleConfirm() {
      if (!this.deliveryDesc) {
        this.$toast('请添加描述')
        return
      }
      if (!this.photoFileList.length && !this.fileList.length) {
        this.$toast('请上传成果')
        return
      }
      const formData = new FormData()
      if (this.photoFileList.length) {
        formData.append('file', this.photoFileList[0].file)
      }
      if (this.fileList.length) {
        formData.append('file', this.fileList[0].file)
      }
      this.loading = true
      const [err, r] = await client.uploadFile({
        body: formData
      })
      if (err) {
        this.loading = false
        return handleError(err)
      }

      if (r.success) {
        const [err2, r2] = await client.personalAddDelivery({
          body: {
            taskId: this.$route.query.taskId,
            fileId: [r.data.fileId],
            deliveryDesc: this.deliveryDesc
          }
        })
        this.loading = false
        if (err2) return handleError(err2)
        this.$toast('交付成功')
        this.$router.go(-1)
      }
    }
  }
}
</script>
<style scoped>
.btn {
  width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #0082fe;
  color: #fff;
  border-radius: 20px;
  padding: 10px;
  margin-left: 16px;
}
</style>
