<template>
  <div class="box">
    <div style="width: 100%" v-if="$route.meta.noNavigationsAndMenus">
      <router-view />
    </div>

    <div
      style="
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      "
      v-if="!$route.meta.noNavigationsAndMenus && loading"
    >
      <div style="font-size: 12px">
        <i class="el-icon-loading" style="color: var(--o-primary-color)"></i>
        加载中
      </div>
    </div>

    <Navigations
      v-if="!$route.meta.noNavigationsAndMenus && !loading"
      :navigations="navigations"
      @change="goNavigation"
      :logoURL="logoURL"
    />
    <div style="display: flex; height: calc(100vh - 65px)" v-if="!loading">
      <transition name="menu-collapse">
        <Menus
          :style="{
            flex: menuCollapsed ? '0 0 0px' : '0 0 240px',
            borderRight: '1px solid #eee',
            overflow: 'hidden'
          }"
          v-if="!$route.meta.noNavigationsAndMenus"
          :navigation="selectedNavigation"
          :collapsed="menuCollapsed"
          @toggle-menu="toggleMenu"
        />
      </transition>

      <!-- 菜单收起时的展开按钮 -->
      <div
        v-if="menuCollapsed && !$route.meta.noNavigationsAndMenus"
        class="collapsed-menu-trigger"
        @click="toggleMenu"
        :title="'展开菜单'"
      >
        <i class="el-icon-d-arrow-right"></i>
      </div>

      <div
        style="overflow-y: auto; width: 100%; background: #f7f7f7"
        v-if="!$route.meta.noNavigationsAndMenus"
      >
        <div
          style="
            padding: 16px 24px 8px 24px;
            background: #fff;
            font-size: 16px;
            font-weight: 550;
          "
        >
          <span
            type="link"
            v-if="!$route.meta.noNeedBack"
            style="
              color: var(--o-primary-color);
              cursor: pointer;
              margin-right: 12px;
            "
            @click="$router.back()"
          >
            返回
          </span>
          {{ $route.meta.title }}
        </div>
        <div
          style="
            flex: 1;
            padding: 12px 24px;
            background: #fff;
            height: calc(100vh - 151px);
            overflow: hidden;
            overflow-y: auto;
          "
        >
          <router-view />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Menus from './menus.vue'
import Navigations from './navigations.vue'
import makeClient from 'kit/services/operateLabor/makeClient'
import handleError from 'kit/helpers/handleError'
const client = makeClient()

export default {
  components: {
    Menus,
    Navigations
  },
  data() {
    return {
      loading: true,
      selectedNavigation: null,
      navigations: [],
      logoURL: '',
      menuCollapsed: false
    }
  },
  async created() {
    if (this.$route.meta.noNavigationsAndMenus) return
    // 从 localStorage 恢复菜单折叠状态
    const savedMenuState = localStorage.getItem('menuCollapsed')
    if (savedMenuState !== null) {
      this.menuCollapsed = JSON.parse(savedMenuState)
    }
    await this.loadNavigations()
  },
  watch: {
    '$route'() {
      // 当路由变化时，更新选中的导航
      this.updateSelectedNavigation()
    }
  },
  mounted() {
    console.log(this.$route.meta)
  },
  methods: {
    goNavigation(navigation) {
      this.selectedNavigation = navigation
      if (this.isCurrentNavigation(navigation)) return
      const path = this.findFirstPathOfNavigation(navigation)
      // 记录当前导航状态
      sessionStorage.setItem('mainNavActiveIndex', this.navigations.indexOf(navigation))
      this.$router.push(path)
    },
    isCurrentNavigation(navigation) {
      const currentPath = this.$route.path
      for (var c of navigation.children) {
        if (c.path === currentPath) return true
        if (c.children && c.children.length) {
          for (var cc of c.children) {
            if (cc.path === currentPath) return true
          }
        }
      }

      return false
    },
    findFirstPathOfNavigation(navigation) {
      if (!navigation.children) return
      for (var c of navigation.children) {
        if (c.path) return c.path
        if (c.children) {
          for (var cc of c.children) {
            if (cc.path) return cc.path
          }
        }
      }
    },
    async loadNavigations() {
      this.loading = true
      const [err, r] = await client.supplierGetMenu()
      this.loading = false
      if (err) {
        handleError(err)
        return
      }

      this.navigations = r.data.children
      // 如果菜单列表为空，跳转到无权限页面
      if (!this.navigations || this.navigations.length === 0) {
        this.$router.push('/noPermission')
        return
      }
      
      // 尝试恢复之前的导航状态
      const savedNavIndex = sessionStorage.getItem('mainNavActiveIndex')
      if (savedNavIndex && this.navigations[savedNavIndex]) {
        this.selectedNavigation = this.navigations[savedNavIndex]
      } else {
        this.selectedNavigation = this.navigations[0]
      }

      const domainInfo = JSON.parse(localStorage.getItem('domainInfo'))
      this.logoURL = domainInfo?.logoUrl

      // 智能判断是否需要自动跳转
      const shouldAutoNavigate = this.$route.path === '/' ||
                                 this.$route.path === '' ||
                                 !sessionStorage.getItem('mainNavActiveIndex')

      if (shouldAutoNavigate) {
        // 查找所有导航中的第一个有效路径并跳转
        var firstPath = this.findFirstPathInAllNavigations()
        if (firstPath) {
          this.$router.push(firstPath)
        } else {
          // 如果没有找到有效路径，跳转到无权限页面
          this.$router.push('/noPermission')
        }
      } else {
        // 如果不需要自动跳转，更新当前选中的导航
        this.updateSelectedNavigation()
      }
    },
    
    findFirstPathInAllNavigations() {
      // 遍历所有导航，查找第一个有 path 属性的菜单项
      for (const navigation of this.navigations) {
        if (navigation.path) return navigation.path
        
        if (navigation.children && navigation.children.length) {
          for (const child of navigation.children) {
            if (child.path) return child.path
            
            if (child.children && child.children.length) {
              for (const grandChild of child.children) {
                if (grandChild.path) return grandChild.path
              }
            }
          }
        }
      }
      
      return null
    },

    updateSelectedNavigation() {
      // 根据当前路由找到对应的导航
      for (let i = 0; i < this.navigations.length; i++) {
        const navigation = this.navigations[i]
        if (this.isCurrentNavigation(navigation)) {
          this.selectedNavigation = navigation
          sessionStorage.setItem('mainNavActiveIndex', i)
          break
        }
      }
    },

    toggleMenu() {
      this.menuCollapsed = !this.menuCollapsed
      // 保存菜单状态到 localStorage
      localStorage.setItem('menuCollapsed', JSON.stringify(this.menuCollapsed))
    }
  }
}
</script>

<style scoped>
/* 菜单折叠动画 */
.menu-collapse-enter-active,
.menu-collapse-leave-active {
  transition: all 0.3s ease;
}

.menu-collapse-enter-from,
.menu-collapse-leave-to {
  flex: 0 0 0px !important;
}

/* 菜单收起时的展开按钮 */
.collapsed-menu-trigger {
  position: fixed;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 32px;
  height: 48px;
  background: linear-gradient(135deg, var(--o-primary-color) 0%, #4facfe 100%);
  border-radius: 0 8px 8px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 2px 0 8px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
  color: white;
  font-size: 14px;
}

.collapsed-menu-trigger:hover {
  width: 36px;
  box-shadow: 2px 0 12px rgba(64, 158, 255, 0.4);
  background: linear-gradient(135deg, #4facfe 0%, var(--o-primary-color) 100%);
}

.collapsed-menu-trigger:active {
  transform: translateY(-50%) scale(0.95);
}

.collapsed-menu-trigger i {
  transition: transform 0.3s ease;
}

.collapsed-menu-trigger:hover i {
  transform: translateX(2px);
}
</style>