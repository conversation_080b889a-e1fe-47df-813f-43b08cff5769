user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    access_log  /var/log/nginx/access.log  main;
    sendfile        on;
    keepalive_timeout  65;   
    client_max_body_size 30M;
                
    server {
        listen 80;
        server_name  localhost;
        
        root /usr/share/nginx/html; # 替换成你项目 dist 目录在服务器上的绝对路径

        #添加对.mjs文件的支持 主要是pdfViwer.mjs
        location ~ \.mjs$ {
            default_type application/javascript;
        }

        location /operate-labor/api/ {
            rewrite ^/operate-labor/api/(.*) /api/$1 break;
            proxy_pass http://olading-operate-labor;
        }
        # merchant 应用
        location /merchant/ {
            try_files $uri $uri/ /merchant.html;
        }
        location = /merchant {
            return 301 /merchant/;
        }

        # platform 应用
        location /platform/ {
            try_files $uri $uri/ /platform.html;
        }
        location = /platform {
            return 301 /platform/;
        }

        # personal 应用
        location /personal/ {
            try_files $uri $uri/ /personal.html;
        }
        location = /personal {
            return 301 /personal/;
        }

        # regulation 应用
        location /regulation/ {
            try_files $uri $uri/ /regulation.html;
        }
        location = /regulation {
            return 301 /regulation/;
        }

        # task 应用
        location /task/ {
            try_files $uri $uri/ /task.html;
        }
        location = /task {
            return 301 /task/;
        }

        # (可选) 根路径重定向，例如重定向到 platform 应用
        location = / {
            return 301 /platform/;
        }

        # (可选) 为静态资源添加缓存策略，提高性能
        location ~* \.(?:jpg|jpeg|gif|png|ico|css|js|mjs)$ {
            expires 7d;
            add_header Cache-Control "public";
        }

        # (可选) 错误页面配置
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

}
