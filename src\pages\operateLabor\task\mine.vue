<template>
  <div class="mine">
    <div class="header-info">
      <img width="48px" src="../../../assets/images/weixin/user.png" alt="" />
      <div
        style="
          display: flex;
          flex-direction: column;
          margin-left: 10px;
          font-size: 12px;
        "
      >
        <div style="font-size: 18px">{{ profile.name }}</div>
        <div>{{ formatterPhone(profile.cellphone) }}</div>
        <div
          style="
            display: flex;
            align-items: center;
            padding: 4px;
            box-sizing: border-box;
          "
          @click="changeCompany"
        >
          {{ corporationName }}
          <img
            width="14px"
            style="margin-left: 8px"
            src="../../../assets/images/weixin/switch.png"
            alt=""
          />
        </div>
      </div>
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 10px;
          font-size: 12px;
          width: 48px;
          height: 20px;
          background: #ffffff4d;
          border: 1px solid #ffffff33;
          border-radius: 4px;
        "
      >
        {{ profile.authStatus ? '已认证' : '未认证' }}
      </div>
    </div>
    <div style="position: absolute; top: 185px; left: 16px; right: 16px">
      <div class="box-content">
        <div class="security-list" @click="toAgreement">
          <div style="display: flex; align-items: center">
            <img src="../../../assets/images/weixin/myAgreement.png" alt="" />
            <div class="list-name">我的协议</div>
          </div>
          <Icon name="arrow" />
        </div>
        <div class="security-list" @click="toReceipt">
          <div style="display: flex; align-items: center">
            <img src="../../../assets/images/weixin/receipt.png" alt="" />
            <div class="list-name">收条</div>
          </div>
          <Icon name="arrow" />
        </div>
        <div class="security-list" @click="toProfile">
          <div style="display: flex; align-items: center">
            <img src="../../../assets/images/weixin/profile.png" alt="" />
            <div class="list-name">个人档案</div>
          </div>
          <Icon name="arrow" />
        </div>
      </div>

      <div class="box-content" style="margin-top: 24px">
        <div class="security-list" @click="toSetting">
          <div style="display: flex; align-items: center">
            <img src="../../../assets/images/weixin/setting.png" alt="" />
            <div class="list-name">设置</div>
          </div>
          <Icon name="arrow" />
        </div>
      </div>
    </div>

    <Popup v-model="showPicker" position="bottom">
      <Picker
        class="picker"
        title="切换企业"
        show-toolbar
        :columns="columns"
        @cancel="showPicker = false"
        @confirm="onConfirm"
      />
    </Popup>
  </div>
</template>
<script>
import { Icon, Popup, Picker } from 'vant'
import { setToken } from 'kit/helpers/token'
import getProfile from '../../../helpers/wxProfile'
import handleError from '../../../helpers/handleErrorH5'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Icon,
    Popup,
    Picker
  },
  data() {
    return {
      profile: {},
      corporationName: '',
      showPicker: false,
      columns: []
    }
  },
  async created() {
    this.profile = await getProfile()
    await this.getCompanyList()
  },
  methods: {
    formatterPhone(phone) {
      if (!phone) return ''
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    },
    async getCompanyList() {
      const [err, r] = await client.listAvailableCorporation()
      if (err) return handleError(err)
      this.columns = r.data.map(item => ({
        text: item.name,
        value: item.id
      }))

      this.corporationName = this.columns.find(
        item => item.value === this.profile.currentCorporationId
      )?.text
    },
    async changeCompany() {
      if (!this.profile.authStatus) {
        this.$router.push({
          path: '/ocr',
          query: {
            source: 'wx-mine'
          }
        })
      } else {
        this.showPicker = true
      }
    },
    async onConfirm(value) {
      const [err, r] = await client.renewToken({
        body: {
          corporationId: value.value
        }
      })
      if (err) return handleError(err)
      console.log('qwer===', r.data.token)
      setToken(r.data.token)
      this.showPicker = false
      window.location.reload()
    },
    isAuth() {
      if (!this.profile.authStatus) {
        this.$router.push({
          path: '/ocr',
          query: {
            source: 'wx-mine'
          }
        })
        return false
      }
      return true
    },
    toAgreement() {
      if(!this.isAuth()) {
        return
      }
      if(!this.corporationName) {
        this.$toast('请先切换作业主体')
        return
      }
      this.$router.push({
        path: '/allAgreement'
      })
    },
    toReceipt() {
      if(!this.isAuth()) {
        return
      }
      if(!this.corporationName) {
        this.$toast('请先切换作业主体')
        return
      }
      this.$router.push({
        path: '/receipt'
      })
    },
    toProfile() {
      if(!this.isAuth()) {
        return
      }
      if(!this.corporationName) {
        this.$toast('请先切换作业主体')
        return
      }
      this.$router.push({
        path: '/personalProfile'
      })
    },
    toSetting() {
      this.$router.push({
        path: '/setting',
        query: {
          cellphone: this.profile.cellphone
        }
      })
    }
  }
}
</script>
<style scoped>
.mine {
  min-height: 100vh;
  background: #fafafa;
  position: relative;
}
.header-info {
  height: 240px;
  box-sizing: border-box;
  color: #ffffff;
  padding: 20px;
  display: flex;
  align-items: center;
  background-image: url('../../../assets/images/weixin/mine-bg.png');
  background-size: cover;
}
.box-content {
  background: #fff;
  border-radius: 8px;
}
.security-list {
  height: 48px;
  line-height: 48px;
  margin: 0 15px 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eef0f4;
}
.security-list img {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}
.list-name {
  font-size: 14px;
  font-weight: 400;
  color: #24262a;
}
</style>
