<template>
  <div class="menus">
    <div
      class="menu-header"
      style="
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eee;
        padding: 0 12px;
        height: 60px;
      "
    >
      <div style="flex: 1; margin-left: 5px; font-weight: 500; color: #303133;">{{ navigation.title }}</div>
      <div
        class="menu-collapse-btn"
        @click="$emit('toggle-menu')"
        :title="collapsed ? '展开菜单' : '收起菜单'"
      >
        <i :class="collapsed ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'"></i>
      </div>
    </div>

    <div class="custom-menu">
      <template v-for="item in navigation.children">
        <!-- Case 1: Item is a parent group with children -->
        <div
          v-if="item.children && item.children.length > 0"
          :key="item.title"
          class="menu-group"
        >
          <div
            class="menu-parent-title"
            :class="{ 'has-active-child': hasActiveChild(item) }"
            @click="toggleGroup(item.title)"
          >
            <span class="menu-parent-text">{{ item.title }}</span>
            <i
              class="menu-arrow"
              :class="[
                isGroupExpanded(item.title) ? 'el-icon-arrow-down' : 'el-icon-arrow-right',
                { 'arrow-active': hasActiveChild(item) }
              ]"
            ></i>
          </div>
          <transition name="menu-slide">
            <div
              v-show="isGroupExpanded(item.title)"
              class="menu-children-container"
            >
              <div
                v-for="subItem in item.children"
                :key="subItem.path"
                class="menu-child-item"
                :class="{ active: $route.path === subItem.path }"
                @click="navigateTo(subItem.path)"
              >
                <span class="menu-child-text">{{ subItem.title }}</span>
                <div v-if="$route.path === subItem.path" class="active-indicator"></div>
              </div>
            </div>
          </transition>
        </div>

        <!-- Case 2: Item is a direct, top-level link (no children) -->
        <div
          v-else-if="item.path"
          :key="item.path"
          class="menu-top-item"
          :class="{ active: $route.path === item.path }"
          @click="navigateTo(item.path)"
        >
          <span class="menu-top-text">{{ item.title }}</span>
          <div v-if="$route.path === item.path" class="active-indicator"></div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    navigation: {
      type: Object,
      default: () => ({ title: '', children: [] })
    },
    collapsed: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      expandedGroups: new Set()
    }
  },
  created() {
    // 初始化时展开包含当前活动路由的分组
    this.initializeExpandedGroups()
  },
  watch: {
    '$route'() {
      // 路由变化时，确保包含当前路由的分组是展开的
      this.initializeExpandedGroups()
    },
    navigation: {
      handler() {
        // 导航数据变化时重新初始化
        this.initializeExpandedGroups()
      },
      deep: true
    }
  },
  methods: {
    navigateTo(path) {
      if (path && this.$route.path !== path) {
        this.$router.push(path)
      }
    },
    toggleGroup(groupTitle) {
      if (this.expandedGroups.has(groupTitle)) {
        this.expandedGroups.delete(groupTitle)
      } else {
        this.expandedGroups.add(groupTitle)
      }
      // 触发响应式更新
      this.$forceUpdate()
    },
    isGroupExpanded(groupTitle) {
      return this.expandedGroups.has(groupTitle)
    },
    hasActiveChild(item) {
      if (!item.children) return false
      return item.children.some(child => child.path === this.$route.path)
    },
    initializeExpandedGroups() {
      if (!this.navigation || !this.navigation.children) return

      // 清空当前展开状态
      this.expandedGroups.clear()

      // 找到包含当前路由的分组并展开
      this.navigation.children.forEach(item => {
        if (item.children && item.children.length > 0) {
          const hasActive = item.children.some(child => child.path === this.$route.path)
          if (hasActive) {
            this.expandedGroups.add(item.title)
          }
        }
      })

      // 如果没有找到活动的分组，默认展开第一个有子项的分组
      if (this.expandedGroups.size === 0) {
        const firstGroupWithChildren = this.navigation.children.find(
          item => item.children && item.children.length > 0
        )
        if (firstGroupWithChildren) {
          this.expandedGroups.add(firstGroupWithChildren.title)
        }
      }
    }
  }
}
</script>

<style scoped>
.custom-menu {
  padding: 8px;
  user-select: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, sans-serif;
}

.menu-group {
  margin-top: 8px;
}

.menu-parent-title {
  font-size: 14px;
  padding: 12px 12px;
  cursor: pointer;
  border-radius: 6px;
  margin: 4px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #606266;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.menu-parent-title:hover {
  background-color: #f5f7fa;
  color: #303133;
}

.menu-parent-title.has-active-child {
  color: var(--o-primary-color);
  background-color: var(--o-primary-bg-color);
}

.menu-parent-text {
  flex: 1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.menu-arrow {
  font-size: 12px;
  transition: transform 0.3s ease, color 0.3s ease;
  color: #85a8ea;
  background: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
}

.menu-arrow.arrow-active {
  color: var(--o-primary-color);
}

.menu-children-container {
  overflow: hidden;
}

.menu-top-item,
.menu-child-item {
  font-size: 14px;
  padding: 12px 12px;
  cursor: pointer;
  border-radius: 6px;
  margin: 2px 0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
}

.menu-child-item {
  padding-left: 32px;
  margin-left: 8px;
  border-left: 2px solid transparent;
}

.menu-top-text,
.menu-child-text {
  flex: 1;
}

.menu-top-item:hover,
.menu-child-item:hover {
  background: linear-gradient(135deg, var(--o-primary-bg-color) 0%, rgba(64, 158, 255, 0.08) 100%);
  transform: translateX(3px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.menu-top-item.active,
.menu-child-item.active {
  color: var(--o-primary-color);
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(79, 172, 254, 0.15) 100%);
  font-weight: 600;
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.menu-top-item.active::before,
.menu-child-item.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.1), transparent);
  transition: left 0.8s;
}

.menu-top-item.active:hover::before,
.menu-child-item.active:hover::before {
  left: 100%;
}

.menu-child-item.active {
  border-left: 3px solid var(--o-primary-color);
  border-radius: 0 6px 6px 0;
}

.active-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--o-primary-color);
  flex-shrink: 0;
  box-shadow: 0 0 4px rgba(64, 158, 255, 0.4);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 4px rgba(64, 158, 255, 0.4);
  }
  50% {
    box-shadow: 0 0 8px rgba(64, 158, 255, 0.6);
  }
  100% {
    box-shadow: 0 0 4px rgba(64, 158, 255, 0.4);
  }
}

/* 菜单折叠按钮样式 */
.menu-collapse-btn {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #85a8ea;
  background: transparent;
  border: 1px solid transparent;
  background: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
}

.menu-collapse-btn:hover {
  background: rgba(64, 158, 255, 0.1);
  color: var(--o-primary-color);
  border-color: rgba(64, 158, 255, 0.2);
  transform: scale(1.05);
}

.menu-collapse-btn:active {
  transform: scale(0.95);
}

.menu-collapse-btn i {
  font-size: 14px;
  transition: transform 0.3s ease;
}

.menu-header:hover .menu-collapse-btn {
  opacity: 1;
}

/* 折叠动画 */
.menu-slide-enter-active,
.menu-slide-leave-active {
  transition: all 0.3s ease;
  transform-origin: top;
}

.menu-slide-enter-from,
.menu-slide-leave-to {
  opacity: 0;
  transform: scaleY(0);
  max-height: 0;
}

.menu-slide-enter-to,
.menu-slide-leave-from {
  opacity: 1;
  transform: scaleY(1);
  max-height: 500px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .custom-menu {
    padding: 4px;
  }

  .menu-parent-title,
  .menu-top-item,
  .menu-child-item {
    padding: 10px 8px;
    font-size: 13px;
  }

  .menu-child-item {
    padding-left: 24px;
  }
}
</style>
