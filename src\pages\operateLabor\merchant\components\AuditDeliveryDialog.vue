<template>
  <el-dialog
    title="审核交付"
    :visible.sync="dialogVisible"
    width="45%"
    :before-close="handleClose"
    class="audit-delivery-dialog"
  >
    <div v-loading="loading">
      <el-form  ref="auditForm" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="审核状态" prop="auditResult">
          <el-select v-model="form.auditResult" placeholder="请选择审核状态">
            <el-option label="审核通过" :value="true"></el-option>
            <el-option label="审核失败" :value="false"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="form.auditResult === false" label="失败原因" prop="auditFailReason">
          <el-input
            v-model="form.auditFailReason"
            type="textarea"
            :rows="3"
            placeholder="请输入审核失败的原因"
            style="width: 50%;"
            maxlength="100"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item v-if="form.auditResult === true" label="任务状态" prop="laborTaskStatus">
          <el-select v-model="form.laborTaskStatus" placeholder="请选择任务状态">
            <el-option label="任务完成" value="COMPLETED"></el-option>
            <el-option label="任务继续" value="IN_PROGRESS"></el-option>
          </el-select>
        </el-form-item>
      </el-form>


      <div v-if="latestDelivery">
        <el-form label-width="100px" class="display-form">
          <el-form-item label="交付时间:">
            <span>{{ latestDelivery.deliverTime | formatDateTime }}</span>
          </el-form-item>
          <el-form-item label="交付描述:">
            <span>{{ latestDelivery.deliveryDesc || '-' }}</span>
          </el-form-item>
          <el-form-item label="上传成果:">
            <div v-if="latestDelivery.fileId && latestDelivery.fileId.length > 0" class="file-grid">
              <div
                v-for="(fileId, fileIndex) in latestDelivery.fileId"
                :key="fileIndex"
                class="file-preview-item"
              >
                <!-- 图片预览 -->
                <div v-if="isImageFile(fileId) && !fileLoadErrors[fileId]" class="image-preview">
                  <div class="preview-image-container">
                    <img
                      :src="getFilePreviewUrl(fileId)"
                      :alt="getFileName(fileId) || `附件${fileIndex + 1}`"
                      class="preview-image"
                      @error="handleImageError(fileId)"
                    />
                  </div>
                  <div class="file-info">
                    <span class="file-name" :title="getFileName(fileId) || `附件${fileIndex + 1}`">{{ getFileName(fileId) || `附件${fileIndex + 1}` }}</span>
                  </div>
                  <div class="file-overlay">
                    <div class="file-actions">
                      <i class="el-icon-zoom-in" @click="previewImage(fileId)" title="预览"></i>
                      <i class="el-icon-download" @click="downloadFile(fileId)" title="下载"></i>
                    </div>
                  </div>
                </div>

                <!-- 视频预览 -->
                <div v-else-if="isVideoFile(fileId)" class="video-preview">
                  <div class="video-icon-container">
                    <i class="el-icon-video-play file-type-icon" style="color: #E6A23C"></i>
                  </div>
                  <div class="file-info">
                    <span class="file-name" :title="getFileName(fileId) || `附件${fileIndex + 1}`">{{ getFileName(fileId) || `附件${fileIndex + 1}` }}</span>
                  </div>
                  <div class="file-overlay">
                    <div class="file-actions">
                      <i class="el-icon-video-play" @click="previewVideo(fileId)" title="播放视频"></i>
                      <i class="el-icon-download" @click="downloadFile(fileId)" title="下载"></i>
                    </div>
                  </div>
                </div>

                <!-- 非图片、非视频文件预览 -->
                <div v-else class="file-preview">
                  <div class="file-icon-container">
                    <i
                      :class="getFileIcon(fileId).icon"
                      class="file-type-icon"
                      :style="{ color: getFileIcon(fileId).color }"
                    ></i>
                  </div>
                  <div class="file-info">
                    <span class="file-name" :title="getFileName(fileId) || `附件${fileIndex + 1}`">{{ getFileName(fileId) || `附件${fileIndex + 1}` }}</span>
                  </div>
                  <div class="file-overlay">
                    <div class="file-actions">
                      <i class="el-icon-download" @click="downloadFile(fileId)" title="下载"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <span v-else>无附件</span>
          </el-form-item>
        </el-form>
      </div>
       <div v-else class="empty-data">
          <i class="el-icon-folder-opened empty-icon"></i>
          <p class="empty-text">暂无交付成果</p>
      </div>
    </div>

    <div class="dialog-actions-center">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">确 定</el-button>
    </div>

    <!-- 图片预览弹窗 -->
    <el-dialog
      :visible.sync="previewVisible"
      :append-to-body="true"
      :show-close="true"
      :modal="true"
      :close-on-click-modal="true"
      width="auto"
      custom-class="image-preview-dialog"
    >
      <img
        v-if="previewImageUrl"
        :src="previewImageUrl"
        style="max-width: 100%; max-height: 70vh"
      />
    </el-dialog>

    <!-- 视频预览弹窗 -->
    <el-dialog
      :visible.sync="videoPreviewVisible"
      :append-to-body="true"
      :show-close="true"
      :modal="true"
      :close-on-click-modal="true"
      width="auto"
      custom-class="video-preview-dialog"
    >
      <video
        v-if="previewVideoUrl"
        :src="previewVideoUrl"
        controls
        style="width: 100%; max-height: 60vh"
        preload="metadata"
      >
        您的浏览器不支持视频播放
      </video>
    </el-dialog>
  </el-dialog>
</template>

<script>
import handleError from '../../../../helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    taskLaborId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      loading: false,
      submitting: false,
      latestDelivery: null,
      fileLoadErrors: {},
      previewVisible: false,
      previewImageUrl: '',
      // 存储文件信息：fileId -> { name, type }
      fileInfoMap: {},
      // 视频预览相关
      videoPreviewVisible: false,
      previewVideoUrl: '',
      form: {
        auditResult: true,
        auditFailReason: '',
        laborTaskStatus: 'IN_PROGRESS'
      },
      rules: {
        auditResult: [{ required: true, message: '请选择审核状态', trigger: 'change' }],
        auditFailReason: [{ required: true, message: '请输入审核失败的原因', trigger: 'blur' }],
        laborTaskStatus: [{ required: true, message: '请选择任务状态', trigger: 'change' }]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.fetchDeliveryData()
      }
    },
    'form.auditResult'(newValue) {
      // 当审核结果切换时，清除另一方的校验提示
      if (this.$refs.auditForm) {
        if (newValue === true) {
          this.$refs.auditForm.clearValidate('auditFailReason')
        } else {
          this.$refs.auditForm.clearValidate('laborTaskStatus')
          // 确保切换到审核失败时，清空失败原因字段
          this.form.auditFailReason = ''
        }
      }
    }
  },
  methods: {
    async fetchDeliveryData() {
      if (!this.taskLaborId) return
      this.loading = true
      try {
        const [err, res] = await client.getDelivery(this.taskLaborId)
        if (err || !res.data || res.data.length === 0) {
          if(err) handleError(err)
          this.latestDelivery = null
          return
        }
        // 只取最后一条交付记录
        this.latestDelivery = res.data[res.data.length - 1]
        
        // 调用描述文件方法获取文件信息
        await this.describeFiles()
      } catch (error) {
        handleError(error)
        this.latestDelivery = null
      } finally {
        this.loading = false
      }
    },
    handleSubmit() {
      this.$refs.auditForm.validate(async (valid) => {
        if (valid) {
          this.submitting = true
          const payload = {
            taskLaborId: this.taskLaborId,
            auditResult: this.form.auditResult,
            auditFailReason: this.form.auditResult ? '' : this.form.auditFailReason,
            laborTaskStatus: this.form.auditResult ? this.form.laborTaskStatus : ''
          }
          try {
            const [err] = await client.auditDelivery({ body: payload })
            if (err) {
              handleError(err)
              return
            }
            this.$message.success('审核操作成功')
            this.$emit('audit-success')
            this.handleClose()
          } catch (error) {
            handleError(error)
          } finally {
            this.submitting = false
          }
        }
      })
    },
    handleClose() {
      this.$refs.auditForm.resetFields()
      this.latestDelivery = null
      this.fileInfoMap = {}
      this.dialogVisible = false
    },
    // --- 文件预览相关方法 ---
    handleImageError(fileId) {
      this.$set(this.fileLoadErrors, fileId, true)
    },
    getFilePreviewUrl(fileId) {
      return `${window.env?.apiPath}/api/public/previewFile/${fileId}`
    },
    downloadFile(fileId) {
      window.location.href = `${window.env?.apiPath}/api/public/downloadFile/${fileId}`
    },
    previewImage(fileId) {
      this.previewImageUrl = this.getFilePreviewUrl(fileId)
      this.previewVisible = true
    },

    previewVideo(fileId) {
      this.previewVideoUrl = this.getFilePreviewUrl(fileId)
      this.videoPreviewVisible = true
    },

    // 获取文件名称
    getFileName(fileId) {
      const fileInfo = this.fileInfoMap[fileId]
      return fileInfo ? fileInfo.name : null
    },

    // 判断是否为图片文件
    isImageFile(fileId) {
      const fileName = this.getFileName(fileId)
      if (!fileName) return false
      
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
      const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
      return imageExtensions.includes(extension)
    },

    // 判断是否为视频文件
    isVideoFile(fileId) {
      const fileName = this.getFileName(fileId)
      if (!fileName) return false
      
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v']
      const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
      return videoExtensions.includes(extension)
    },

    getFileIcon(fileId) {
      const fileName = this.getFileName(fileId)
      if (!fileName) {
        return { icon: 'el-icon-document', color: '#409eff' }
      }
      
      const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
      
      // 根据文件扩展名返回不同的图标
      if (['.pdf'].includes(extension)) {
        return { icon: 'el-icon-document', color: '#5470c6' }
      } else if (['.doc', '.docx'].includes(extension)) {
        return { icon: 'el-icon-document', color: '#5470c6' }
      } else if (['.xls', '.xlsx'].includes(extension)) {
        return { icon: 'el-icon-s-grid', color: '#5470c6' }
      } else if (['.zip', '.rar', '.7z'].includes(extension)) {
        return { icon: 'el-icon-folder-opened', color: '#5470c6' }
      } else {
        return { icon: 'el-icon-document-copy', color: '#5470c6' }
      }
    },

    // 描述文件方法
    async describeFiles() {
      if (!this.latestDelivery || !this.latestDelivery.fileId || this.latestDelivery.fileId.length === 0) {
        return
      }

      console.log('开始调用描述文件方法...')
      console.log(`交付记录包含 ${this.latestDelivery.fileId.length} 个文件`)
      
      // 遍历每个文件ID
      for (let fileIndex = 0; fileIndex < this.latestDelivery.fileId.length; fileIndex++) {
        const fileId = this.latestDelivery.fileId[fileIndex]
        console.log(`准备查询文件ID: ${fileId}`)
        
        try {
          // 调用描述文件接口
          const [err, response] = await this.callDescribeFile(fileId)
          
          if (err) {
            console.error(`查询文件 ${fileId} 失败:`, err)
          } else {
            console.log(`文件ID: ${fileId}`, response)
            
            // 存储文件信息到 fileInfoMap 中
            if (response && response.data && response.data.name) {
              this.$set(this.fileInfoMap, fileId, {
                name: response.data.name,
                type: this.getFileTypeFromName(response.data.name)
              })
              console.log(`文件 ${fileId} 信息已存储:`, this.fileInfoMap[fileId])
            }
          }
        } catch (error) {
          console.error(`查询文件 ${fileId} 异常:`, error)
        }
      }
    },

    // 调用描述文件接口
    async callDescribeFile(fileId) {
      const requestBody = {
        id: fileId
      }

      return await client.describeFile({
        body: requestBody
      })
    },

    // 根据文件名获取文件类型
    getFileTypeFromName(fileName) {
      if (!fileName) return 'unknown'
      
      const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
      
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v']
      
      if (imageExtensions.includes(extension)) {
        return 'image'
      } else if (videoExtensions.includes(extension)) {
        return 'video'
      } else {
        return 'document'
      }
    }
  },
  filters: {
    formatDateTime(value) {
      if (!value) return ''
      return new Date(value).toLocaleString('zh-CN', { hour12: false })
    }
  }
}
</script>

<style scoped>
.display-form .el-form-item {
  margin-bottom: 10px;
}

.display-form span {
  color: #606266;
  font-size: 14px;
}
.info-item {
  color: #606266;
  font-weight: normal;
  margin-bottom: 10px;
}
.empty-data {
  padding:30px 50px 10px 100px;
  min-height: 150px;
  color: #909399;
}
.empty-icon {
  font-size: 48px;
  margin-bottom: 15px;
}
.empty-text {
  font-size: 14px;
}
.file-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
}
.file-preview-item {
  position: relative;
  width: 160px;
  height: 140px;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}
.file-preview-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #409eff;
}
.file-preview-item:hover .file-overlay {
  opacity: 1;
}
.image-preview, .file-preview, .video-preview {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.preview-image-container, .file-icon-container, .video-icon-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  padding: 8px;
  overflow: hidden;
}
.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}
.file-type-icon {
  font-size: 48px;
}
.file-info {
  padding: 8px 12px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  height: 36px;
  box-sizing: border-box;
}
.file-name {
  font-size: 12px;
  color: #606266;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.file-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;
}
.file-actions {
  display: flex;
  gap: 16px;
}
.file-actions i {
  cursor: pointer;
  font-size: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.file-actions i:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* 按钮居中样式 */
.dialog-actions-center {
  display: flex;
  justify-content: center;
  margin: 20px 0px 20px 0px;
  gap: 20px;
}

/* 预览弹窗样式 */
::v-deep .image-preview-dialog {
  text-align: center;
  width: auto !important;
}

::v-deep .image-preview-dialog .el-dialog__body {
  padding: 20px;
}

::v-deep .video-preview-dialog {
  .el-dialog__body {
    padding: 20px;
    text-align: center;
  }
}
</style>