/* 表格滚动条美化样式 */

/* 水平滚动条 */
.el-table__body-wrapper::-webkit-scrollbar {
  height: 6px;
}

.el-table__body-wrapper::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 2px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 垂直滚动条 */
.el-table__body-wrapper::-webkit-scrollbar:vertical {
  width: 6px;
}

/* 滚动条角落 */
.el-table__body-wrapper::-webkit-scrollbar-corner {
  background: rgba(0, 0, 0, 0.1);
}

/* 通用滚动条样式 - 可用于其他组件 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: rgba(0, 0, 0, 0.1);
}

.el-table__fixed-right-patch {
  background: var(--o-primary-bg-color);
}
