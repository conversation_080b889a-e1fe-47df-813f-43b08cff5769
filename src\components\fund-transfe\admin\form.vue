<template>
  <el-form
    ref="form"
    size="small"
    v-bind="$attrs"
    @submit.native.prevent
    v-on="$listeners"
    class="marketing-custom-form"
  >
    <slot></slot>
  </el-form>
</template>

<script>
export default {
  methods: {
    clearValidate() {
      this.$refs.form.clearValidate()
    },
    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => {
          if (valid) return resolve(null)
          resolve('Form validation failed')
          this.scrollToError()
        })
      })
    },
    async scrollToError() {
      await this.$nextTick()
      const fieldEl = this.$el.querySelector('.is-error')
      fieldEl.scrollIntoView({
        behavior: 'smooth'
      })
    },
    validateField(prop) {
      this.$refs.form.validateField(prop)
    },
    resetForm() {
      this.$refs.form.resetFields()
      this.$nextTick(() => {
        this.clearValidate()
      })
    }
  }
}
</script>

<style scoped>
::v-deep.marketing-custom-form.el-form--label-top .el-form-item__label {
  margin-bottom: 8px;
  height: 22px;
  padding: 0;
  line-height: 22px;
}
::v-deep button:focus {
  outline: none;
}
</style>
