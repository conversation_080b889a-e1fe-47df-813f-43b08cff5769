<template>
  <div class="menus">
    <header>
      <el-image style="width: 24px; height: 24px" :src="icon"></el-image>
      <h1>{{ menuTitle }}</h1>
    </header>
    <div class="menus-container">
      <el-menu
        :default-active="activeCode"
        :collapse="isCollapsed"
        :unique-opened="true"
        :router="true"
      >
        <template v-for="item in menuList">
          <el-submenu v-if="item.children" :index="item.code" :key="item.path">
            <template #title>
              <span>{{ item.title }}</span>
            </template>
            <el-menu-item
              v-for="child in item.children"
              :index="child.code"
              :route="child.path"
              :key="child.path"
            >
              <span>{{ child.title }}</span>
            </el-menu-item>
          </el-submenu>
          <el-menu-item
            v-else
            :index="item.code"
            :route="item.path"
            :key="item.path"
          >
            <span>{{ item.title }}</span>
          </el-menu-item>
        </template>
      </el-menu>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    menuList: {
      type: Array,
      default: () => []
    },
    icon: {
      type: String,
      default: ''
    },
    menuTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeCode: '',
      isCollapsed: false
    }
  },
  watch: {
    $route() {
      this.setActiveMenuCode(this.$route)
    }
  },
  created() {
    this.setActiveMenuCode(this.$route)
  },
  methods: {
    setActiveMenuCode() {
      const { menuCode, parentMenuCode } = this.$route.meta
      this.activeCode = menuCode || parentMenuCode
    }
  }
}
</script>
<style scoped>
::v-deep.el-menu {
  border-right: 0;
}
::v-deep .el-menu-item,
::v-deep .el-submenu__title {
  height: 40px;
  line-height: 40px;
  color: #1e2228ff;
  font-size: 14px;
  font-weight: 600;
  font-family: 'PingFang SC';
  text-align: left;
  margin-bottom: 2px;
}
::v-deep .el-menu-item:focus,
::v-deep .el-menu-item:hover,
::v-deep .is-active.el-menu-item {
  background: #fff3e8ff;
  color: #f77234ff;
  border-radius: 8px;
}
::v-deep .is-active .el-submenu__title,
::v-deep .el-submenu__title:hover {
  background: #fff;
}
.menus {
  background: #fff;
  height: 100%;
}
header {
  display: flex;
  align-items: center;
  height: 56px;
  margin: 0 24px;
  border-bottom: 1px solid #e4e7edff;
  margin-bottom: 16px;
}
header h1 {
  color: #1e2228ff;
  font-size: 16px;
  margin: 0;
  margin-left: 12px;
  font-weight: 600;
  font-family: 'PingFang SC';
}
.menus-container .el-menu {
  padding: 0 16px;
}
</style>
