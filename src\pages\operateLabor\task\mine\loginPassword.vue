<template>
  <div
    class="securityPasswordForgetChange"
    style="
      height: 100vh;
      background: #f4f4f4;
      padding: 20px 16px;
      box-sizing: border-box;
    "
  >
    <NavBar left-text="返回" style="margin-bottom: 1px;" left-arrow @click-left="handleBack" />
    <Field
      v-model="phone"
      type="tel"
      placeholder="请通过手机号登录"
      maxlength="11"
      clearable
      left-icon="phone-o"
      disabled
    />

    <Captcha ref="captcha" style="background: #ffffff" v-model="captcha" />

    <Field
      v-model="code"
      type="text"
      placeholder="请输入验证码"
      maxlength="6"
      left-icon="shield-o"
    >
      <template #button>
        <a
          size="small"
          type="text"
          :disabled="smsCountdown > 0"
          @click="sendSmsCode"
          class="sms-btn"
          style="color: #4285f4; cursor: pointer"
        >
          {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
        </a>
      </template>
    </Field>
    <Field
      v-model="newPassword1"
      type="password"
      maxlength="20"
      placeholder="6-20位数字、字母和符号"
    />
    <Field
      v-model="newPassword2"
      type="password"
      maxlength="20"
      placeholder="请再次输入密码"
    />
    <Button
      style="border: none; margin-top: 20px; background: #4F71FF"
      @click="updateSecurityPassword"
      block
      round
      type="primary"
      >确定</Button
    >
  </div>
</template>

<script>
import { Field, Button, NavBar } from 'vant'
import Captcha from '../captcha.vue'
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Field,
    Button,
    Captcha,
    NavBar
  },
  data() {
    return {
      phone: '',
      captcha: {},
      code: '',
      smsCodeToken: '',
      newPassword1: '',
      newPassword2: '',
      smsCountdown: 0,
      smsTimer: null
    }
  },
  created() {
    this.phone = this.$route.query.cellphone
  },
  methods: {
    async sendSmsCode() {
      if (this.phone.length !== 11) {
        this.$toast('请输入正确的手机号')
        return
      }
      if (!this.captcha.value || this.captcha.value.length !== 4) {
        this.$toast('请输入正确的图形验证码')
        return
      }

      // 开始倒计时
      this.smsCountdown = 60
      this.smsTimer = setInterval(() => {
        this.smsCountdown--
        if (this.smsCountdown <= 0) {
          clearInterval(this.smsTimer)
          this.smsTimer = null
        }
      }, 1000)

      this.sendLoginSms()
    },
    async sendLoginSms() {
      const [err, r] = await client.sendOtp({
        body: {
          captchaToken: this.captcha.token,
          captchaAnswer: this.captcha.value,
          businessType: 'OLD_land_diy'
        }
      })
      if (err) {
        handleError(err)
        clearInterval(this.smsTimer)
        this.smsTimer = null
        this.smsCountdown = 0
        this.captcha.value = ''
        // 刷新图像验证码
        this.$refs.captcha?.refreshCaptcha()
        return
      }
      this.smsCodeToken = r.data.token
    },
    async updateSecurityPassword() {
      // 校验手机号
      if (!this.phone || this.phone.length !== 11) {
        this.$toast('请输入正确的手机号')
        return
      }

      // 校验图形验证码
      if (!this.captcha.value || this.captcha.value.length !== 4) {
        this.$toast('请输入正确的图形验证码')
        return
      }

      // 校验是否已获取短信验证码
      if (!this.smsCodeToken) {
        this.$toast('请先获取短信验证码')
        return
      }

      // 校验短信验证码
      if (!this.code) {
        this.$toast('请输入短信验证码')
        return
      }

      // 校验密码输入
      if (!this.newPassword1) {
        this.$toast('请输入新密码')
        return
      }

      if (!this.newPassword2) {
        this.$toast('请再次输入密码')
        return
      }

      // 校验两次密码一致性
      if (this.newPassword1 !== this.newPassword2) {
        this.$toast('两次密码输入不一致')
        return
      }

      // 校验密码格式（6-20位数字、字母和符号）
      if (this.newPassword1.length < 6 || this.newPassword1.length > 20) {
        this.$toast('密码长度应为6-20位')
        return
      }

      const body = {
        code: this.code,
        otpToken: this.smsCodeToken,
        password: this.newPassword1,
        confirmPassword: this.newPassword2
      }

      const [err, r] = await client.personalSetPassword({ body })
      if (err) return handleError(err)

      this.$toast('密码设置成功')
      setTimeout(() => {
        this.$router.go(-1)
      }, 1000)
    },
    handleBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style></style>
