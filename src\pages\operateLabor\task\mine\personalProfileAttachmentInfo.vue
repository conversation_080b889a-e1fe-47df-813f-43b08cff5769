<template>
  <div class="attachmentInfo" style="height: 100vh; box-sizing: border-box">
    <NavBar left-text="返回" left-arrow @click-left="onClickLeft" />
    <div style="padding: 16px">
      <div>附件信息</div>
    <div style="color: #9f9f9f; font-size: 12px; margin-bottom: 10px">
      <div>可上传证件、技能证书、简历附件等。</div>
      <div>支持JPG、PNG、JPEG、PDF格式的文件，大小不超过10M</div>
    </div>
    <div style="display: flex; flex-wrap: wrap; gap: 25px; margin-bottom: 10px">
      <div
        style="position: relative; width: 80px; height: 80px"
        v-for="(item, index) in photos"
        :key="index"
      >
        <img
          v-if="isImageItem(item)"
          style="
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
          "
          :src="getPreviewUrl(item)"
          :alt="`附件${index + 1}`"
          @error="() => handleImageError(index)"
          @click="previewImage(index)"
        />
        <div
          v-else
          style="
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;
            border-radius: 4px;
            border: 1px solid #ddd;
          "
        >
          📄
        </div>
        <div
          style="
            position: absolute;
            top: 0;
            right: 0;
            width: 16px;
            height: 16px;
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            color: #fff;
            font-size: 12px;
            line-height: 16px;
            text-align: center;
            cursor: pointer;
          "
          @click="remove(index)"
        >
          X
        </div>
      </div>
    </div>
    <Uploader
      v-model="fileList"
      :after-read="afterRead"
      accept="image/*, application/pdf"
    ></Uploader>
      <Button class="btn" @click="confirm" :loading="loading">确 定</Button>
    </div>
  </div>
</template>
<script>
import { Uploader, Button, NavBar, ImagePreview } from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Uploader,
    Button,
    NavBar
  },
  data() {
    return {
      fileList: [],
      fileId: '',
      loading: false,
      photos: [],
      pendingFiles: [] // 存储待上传的文件
    }
  },
  created() {
    this.init()
  },
  methods: {
    onClickLeft() {
      this.$router.push('/personalProfile')
    },
    async init() {
      const { laborInfoId } = this.$route.query
      const [err, r] = await client.apiLaborLaborDetail(laborInfoId)
      if (err) return handleError(err)
      this.photos = r.data.photos
      console.log('fileList===', this.fileList)
    },
    isImage(item) {
      if (item instanceof File) {
        return item.type.startsWith('image/')
      }
      return true
    },
    isImageItem(item) {
      if (item instanceof File) {
        return item.type.startsWith('image/')
      }
      if (typeof item === 'object' && item.isImage === false) {
        return false
      }
      return true
    },
    handleImageError(index) {
      const currentItem = this.photos[index]
      if (typeof currentItem === 'string') {
        this.$set(this.photos, index, { id: currentItem, isImage: false })
      }
    },
    getPreviewUrl(item) {
      if (item instanceof File) {
        const url = URL.createObjectURL(item)
        return url
      }
      if (typeof item === 'object' && item.id) {
        const url = `${window.env?.apiPath}/api/public/previewFile/${item.id}`
        return url
      }
      const url = `${window.env?.apiPath}/api/public/previewFile/${item}`
      return url
    },
    remove(index) {
      const removedItem = this.photos[index]
      this.photos.splice(index, 1)
      
      if (removedItem instanceof File) {
        this.pendingFiles = this.pendingFiles.filter(file => file !== removedItem)
      }
    },
    async afterRead(file) {
      if (file.file && file.file.size > 10 * 1024 * 1024) {
        this.$toast('文件大小不能超过10MB')
        this.fileList = this.fileList.filter(item => item.file.size <= 10 * 1024 * 1024)
        return
      }
      
      if (Array.isArray(file)) {
        file.forEach(item => {
          this.photos.push(item.file)
          this.pendingFiles.push(item.file)
        })
      } else {
        this.photos.push(file.file)
        this.pendingFiles.push(file.file)
      }
      
      this.$nextTick(() => {
        this.fileList = []
      })
    },
    previewImage(index) {
      // 只预览图片，过滤掉非图片文件
      const imageItems = this.photos.filter(item => this.isImageItem(item))
      const imageUrls = imageItems.map(item => this.getPreviewUrl(item))
      
      // 找到当前点击图片在图片列表中的索引
      const currentImageIndex = imageItems.findIndex(imageItem => {
        const currentItem = this.photos[index]
        if (currentItem instanceof File && imageItem instanceof File) {
          return currentItem === imageItem
        }
        if (typeof currentItem === 'string' && typeof imageItem === 'string') {
          return currentItem === imageItem
        }
        if (typeof currentItem === 'object' && typeof imageItem === 'object') {
          return currentItem.id === imageItem.id
        }
        return this.getPreviewUrl(currentItem) === this.getPreviewUrl(imageItem)
      })
      
      ImagePreview({
        images: imageUrls,
        startPosition: currentImageIndex >= 0 ? currentImageIndex : 0,
        closeable: true
      })
    },
    async confirm() {
      this.loading = true
      
      // 只上传pendingFiles中的文件
      if(this.pendingFiles.length) {
        for(var file of this.pendingFiles) {
          const formData = new FormData()
          formData.append('file', file)
          const [err, r] = await client.uploadFile({
            body: formData
          })
          if (err) {
            this.loading = false
            handleError(err)
            return
          }
          
          // 将photos数组中的文件对象替换为文件ID
          const fileIndex = this.photos.indexOf(file)
          if (fileIndex !== -1) {
            this.photos[fileIndex] = r.data.fileId
          }
        }
      }
      
      // 提取所有文件ID，处理可能的对象结构
      const photoIds = this.photos.map(item => {
        if (typeof item === 'object' && item.id) {
          return item.id
        }
        return item
      })
      
      const [err2, r2] = await client.apiLaborEditLaborDetail({
        body: {
          laborId: this.$route.query.laborInfoId,
          basicInfo: null,
          photos: photoIds,
          projectHistory: null,
          laborBankCard: null
        }
      })
      this.loading = false
      if (err2) return handleError(err2)
      
      // 清空待上传文件列表
      this.pendingFiles = []
      this.fileList = []
      this.$toast('上传成功')
      this.$router.push('/personalProfile')
    }
  }
}
</script>
<style scoped>
.btn {
  width: 300px;
  position: fixed;
  left: 10vw;
  bottom: 20px;
  text-align: center;
  box-sizing: border-box;
  background: #0281ff;
  color: #fff;
  border-radius: 20px;
}
</style>