import { getToken } from '../../utils/token.js'

Page({
  data: {
    webviewSrc: ''
  },

  onLoad() {
    const token = getToken()

    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return
    }

    this.setData({
      webviewSrc: `https://linggong.olading.com/task/myTask?token=${token}`
    })
  }
})


