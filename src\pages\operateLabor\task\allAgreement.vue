<template>
  <div
    class="allAgreement"
    style="
      background: #f5f5f5;
      min-height: 100vh;
      padding: 16px 16px 0;
      box-sizing: border-box;
    "
  >
    <NavBar left-text="返回" left-arrow @click-left="onClickLeft" />
    <div
      style="
        background: #ffffff;
        padding: 20px 10px 20px 20px;
        border-radius: 10px;
      "
      v-for="(item, index) in contractList"
      :key="index"
      @click="goSign(item)"
    >
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
        "
      >
        <div class="flex-box">
          <div class="contract__title">{{ item.protocolName }}</div>
          <div style="display: flex; align-items: center">
            <div
              style="
                width: 20px;
                height: 20px;
                border-radius: 2.5px;
                margin-right: 4px;
              "
            >
              <img width="20px" src="../../../assets/images/weixin/fa.png" />
            </div>
            <span style="font-weight: 400; font-size: 14px; color: #828b9b">{{
              item.corporationName
            }}</span>
          </div>
        </div>
        <div style="display: flex; align-items: center">
          <div class="flex-box" style="margin-right: 10px">
            <div class="contract__time">
              {{ item.createdTime.slice(0, 10) }}
            </div>
            <div
              :style="{
                fontWeight: '500',
                fontSize: '12px',
                color: '#3369ff',
                textAlign: 'right',
                color: getColor(item.contractSignStatus)
              }"
            >
              {{ formatterStatus(item.contractSignStatus) }}
            </div>
          </div>
          <van-icon style="color: #d6d6d6" name="arrow" />
        </div>
      </div>
    </div>
    <div v-if="!contractList.length" class="nothing-data">
      <img src="../../../assets/images/no-data.png" alt />
      <p>暂无任何待办事项</p>
    </div>
  </div>
</template>
<script>
import { Icon, NavBar } from 'vant'
import handleError from '../../../helpers/handleErrorH5'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    [Icon.name]: Icon,
    NavBar
  },
  data() {
    return {
      contractList: []
    }
  },
  created() {
    this.getContractList()
  },
  methods: {
    onClickLeft() {
      this.$router.push('/mine')
    },
    async getContractList() {
      const [err, r] = await client.personalGetLaborAllContract()
      if (err) {
        handleError(err)
        return
      }
      this.contractList = r.data
    },
    getColor(value) {
      switch (value) {
        case 'ACCEPT':
          return '#FF9A01'
        case 'IN_PROCESS':
          return '#4F71FF'
        case 'SUCCESS':
          return '#07BB06'
        default:
          return ''
      }
    },
    formatterStatus(value) {
      switch (value) {
        case 'ACCEPT':
          return '待签署'
        case 'IN_PROCESS':
          return '签署中'
        case 'SUCCESS':
          return '签署完成'
        default:
          return '待签署'
      }
    },
    goSign(item) {
      this.$router.push({
        path: '/showContract',
        query: {
          source: 'mine-allAgreement',
          archiveId: item.archiveId,
          protocolId: item.protocolId,
          contractSignStatus: item.contractSignStatus
        }
      })
    }
  }
}
</script>
<style scoped>
.flex-box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 12px;
}
.contract__title {
  width: 176px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  font-weight: 600;
  font-size: 16px;
  color: #1e2228;
}
.contract__time {
  font-weight: 400;
  font-size: 12px;
  color: #828b9b;
}
.nothing-data {
  width: 100%;
  height: 80vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
::v-deep .van-icon {
  color: #d6d6d6;
}
</style>
