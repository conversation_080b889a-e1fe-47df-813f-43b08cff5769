<template>
  <el-date-picker
    v-model="dateValue"
    :type="type"
    :value-format="valueFormat"
    ref="datePicker"
    v-bind="$attrs"
    range-separator="至"
    start-placeholder="开始日期"
    @change="change"
    end-placeholder="结束日期"
  ></el-date-picker>
</template>

<script>
export default {
  props: {
    valueFormat: {
      type: String,
      default: 'yyyy-MM-dd HH:mm:ss'
    },
    type: {
      type: String,
      default: 'daterange'
    },
    endTime: {
      type: String,
      default: ''
    },
    startTime: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dateValue: [],
      elFormItem: null
    }
  },
  watch: {
    endTime: 'initDateValue',
    startTime: 'initDateValue'
  },
  mounted() {
    this.initDateValue()
  },
  methods: {
    async change() {
      const [startTime, endTime] = this.dateValue || []
      this.$emit('update:startTime', startTime)
      this.$emit('update:endTime', endTime)
      this.onInput()
    },
    findElFormItem(component) {
      if (component.$options.name === 'ElFormItem') {
        return component
      } else if (component.$parent) {
        return this.findElFormItem(component.$parent)
      }
      return null
    },
    async onInput() {
      this.$emit('input', this.dateValue)
    },
    initDateValue() {
      if (!this.startTime) return
      this.dateValue = [this.startTime, this.endTime]
    }
  }
}
</script>
