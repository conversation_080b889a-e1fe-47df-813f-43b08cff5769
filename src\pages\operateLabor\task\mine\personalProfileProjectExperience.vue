<template>
  <div class="projectExperience">
    <NavBar left-text="返回" left-arrow @click-left="onClickLeft" />
    <div style="padding: 20px 10px">
      <div v-for="(item, index) in experienceList" :key="index">
      <Form ref="form" @submit="onSubmit(item)">
        <div
          style="
            background-color: #f2f2f2;
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <div>
            <span style="color: #4983ee">|</span>
            <span style="color: #7f7f7f">项目经历</span>
          </div>
          <div>
            <Button
              class="btn"
              style="background: #277dfe"
              v-if="!item.id"
              native-type="submit"
              >保存</Button
            >
            <Button
              class="btn"
              v-if="item.id"
              style="background: #a2c1f7"
              disabled
              >已保存</Button
            >
            <Button
              class="btn"
              style="background: #fefefe; color: #4c4c4c"
              native-type="button"
              @click="handleDelete(item)"
              v-if="shouldShowDeleteButton(item, index)"
              >删除</Button
            >
          </div>
        </div>
        <Field
          v-model="item.projectName"
          label="项目名称"
          placeholder="请输入项目名称"
          maxlength="100"
          :rules="[{ required: true, message: '' }]"
          :readonly="!!item.id"
        />
        <Field
          v-model="item.post"
          label="担任角色"
          placeholder="请输入担任角色"
          maxlength="50"
          :rules="[{ required: true, message: '' }]"
          :readonly="!!item.id"
        />
        <CellGroup>
          <Field
            v-model="item.projectStart"
            label="项目时间"
            readonly
            :clickable="!item.id"
            :rules="[{ required: true, message: '请选择项目时间' }]"
          >
            <template #input>
              <div style="color: #cbcbce; display: flex">
                <div @click="!item.id && openStartPicker(item, index)" :style="{ cursor: item.id ? 'not-allowed' : 'pointer' }">
                  {{ item.projectStart || '开始时间' }}
                </div>
                <div style="margin: 0 30px">-</div>
                <div @click="!item.id && openEndPicker(item, index)" :style="{ cursor: item.id ? 'not-allowed' : 'pointer' }">
                  {{ item.projectEnd || '结束时间' }}
                </div>
              </div>
            </template>
          </Field>
        </CellGroup>

        <Popup :value="showStartPicker === index" @input="val => !val && onCancelStartPicker(item)" position="bottom">
          <DatetimePicker
            v-model="tempStartDate"
            type="date"
            title="选择开始时间"
            @confirm="onConfirmStart(item, index)"
            @cancel="onCancelStartPicker(item)"
          />
        </Popup>

        <Popup :value="showEndPicker === index" @input="val => !val && onCancelEndPicker(item)" position="bottom">
          <DatetimePicker
            v-model="tempEndDate"
            type="date"
            title="选择结束时间"
            @confirm="onConfirmEnd(item, index)"
            @cancel="onCancelEndPicker(item)"
          />
        </Popup>
        <Field
          v-model="item.description"
          label="项目描述"
          placeholder="请输入项目描述"
          type="textarea"
          autosize
          maxlength="1000"
          show-word-limit
          :rules="[{ required: true, message: '' }]"
          :readonly="!!item.id"
        />
        <Field
          v-model="item.performance"
          label="项目业绩"
          placeholder="请输入项目业绩"
          type="textarea"
          autosize
          maxlength="500"
          show-word-limit
          :rules="[{ required: true, message: '' }]"
          :readonly="!!item.id"
        />
      </Form>
    </div>
    
      <!-- 添加项目经历按钮 -->
      <div class="add-experience-btn" @click="addExperience">
        + 项目经历
      </div>
    </div>
  </div>
</template>
<script>
import {
  Icon,
  Form,
  Field,
  CellGroup,
  Popup,
  DatetimePicker,
  Button,
  Dialog,
  NavBar
} from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Icon,
    Form,
    Field,
    CellGroup,
    Popup,
    DatetimePicker,
    Button,
    Dialog,
    NavBar
  },
  data() {
    return {
      experienceList: [],
      showStartPicker: false,
      showEndPicker: false,
      tempStartDate: null,
      tempEndDate: null
    }
  },
  created() {
    this.init()
  },
  methods: {
    onClickLeft() {
      this.$router.push('/personalProfile')
    },
    async init() {
      const { laborInfoId } = this.$route.query
      const [err, r] = await client.apiLaborLaborDetail(laborInfoId)
      if (err) return handleError(err)
      this.experienceList = r.data.projectHistory
      if (!this.experienceList.length) {
        this.experienceList.push({
          key: Date.now(),
          projectName: '',
          post: '',
          projectStart: '',
          projectEnd: '',
          description: '',
          performance: ''
        })
      }
    },
    addExperience() {
      this.experienceList.push({
        key: Date.now(),
        projectName: '',
        post: '',
        projectStart: '',
        projectEnd: '',
        description: '',
        performance: ''
      })
    },
    openStartPicker(item, index) {
      this.tempStartDate = item.projectStart ? new Date(item.projectStart) : new Date()
      this.showStartPicker = index
    },
    openEndPicker(item, index) {
      this.tempEndDate = item.projectEnd ? new Date(item.projectEnd) : new Date()
      this.showEndPicker = index
    },
    onConfirmStart(item, index) {
      console.log('item===', item)
      item.projectStart = this.formatDate(this.tempStartDate)
      this.showStartPicker = false
    },
    onConfirmEnd(item, index) {
      item.projectEnd = this.formatDate(this.tempEndDate)
      this.showEndPicker = false
    },
    onCancelStartPicker(item) {
      this.showStartPicker = false
      // 恢复原始值，不做任何更改
    },
    onCancelEndPicker(item) {
      this.showEndPicker = false
      // 恢复原始值，不做任何更改
    },
    formatDate(date) {
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(
        2,
        '0'
      )}-${String(d.getDate()).padStart(2, '0')}`
    },
    async onSubmit(currentItem) {
      // 验证结束时间不能小于开始时间
      if (currentItem.projectStart && currentItem.projectEnd) {
        const startDate = new Date(currentItem.projectStart)
        const endDate = new Date(currentItem.projectEnd)
        if (endDate < startDate) {
          this.$toast('结束时间不能小于开始时间')
          return
        }
      }
      
      // 只保存当前项目经历
      const [err, r] = await client.apiLaborEditLaborDetail({
        body: {
          laborId: this.$route.query.laborInfoId,
          basicInfo: null,
          photos: null,
          projectHistory: [currentItem], // 只传当前项目经历
          laborBankCard: null
        }
      })
      if (err) return handleError(err)
      this.$toast('保存成功')
      
      // 不重新初始化，而是更新当前项目经历的状态
      // 从服务器获取最新的项目经历ID
      const { laborInfoId } = this.$route.query
      const [detailErr, detailR] = await client.apiLaborLaborDetail(laborInfoId)
      if (detailErr) return handleError(detailErr)
      
      // 找到刚保存的项目经历并更新其ID
      const savedProjects = detailR.data.projectHistory
      const currentIndex = this.experienceList.findIndex(item => item === currentItem)
      
      if (currentIndex !== -1 && savedProjects.length > 0) {
        // 找到最新保存的项目经历（通常是最后一个或者匹配项目名称的）
        const matchedProject = savedProjects.find(saved => 
          saved.projectName === currentItem.projectName && 
          saved.post === currentItem.post &&
          saved.description === currentItem.description
        ) || savedProjects[savedProjects.length - 1]
        
        // 更新当前项目经历的ID，使其变为已保存状态
        this.$set(this.experienceList, currentIndex, { ...currentItem, id: matchedProject.id })
      }
    },
    handleDelete(item) {
      console.log('handleDelete',item)
      const { id, laborId } = item
      if(!id) {
        // 对于未保存的项目经历，直接从列表中移除当前项
        const index = this.experienceList.findIndex(exp => exp.key === item.key)
        if (index > -1) {
          this.experienceList.splice(index, 1)
        }
        return
      }
      Dialog.confirm({
        title: '标题',
        message: '确定要删除此条项目经历吗？'
      })
        .then(async () => {
          const [err, r] = await client.personalDelLaborProjectHisById(
            id,
            laborId
          )
          if (err) return handleError(err)
          this.$toast('删除成功')
          this.init()
        })
        .catch(() => {})
    },
    shouldShowDeleteButton(item, index) {
      // 如果有ID说明是已保存的项目经历，可以删除
      if (item.id) {
        return true
      }
      
      // 统计已保存和未保存的项目经历数量
      const savedCount = this.experienceList.filter(exp => exp.id).length
      const unsavedCount = this.experienceList.filter(exp => !exp.id).length
      
      // 如果有已保存的项目经历，未保存的也可以删除
      if (savedCount > 0) {
        return true
      }
      
      // 如果没有已保存的项目经历，且未保存的只有一个，不显示删除按钮
      if (savedCount === 0 && unsavedCount === 1) {
        return false
      }
      
      // 其他情况可以删除
      return true
    }
  }
}
</script>
<style scoped>
.btn {
  height: 28px;
  box-sizing: border-box;
  color: #fff;
  border-radius: 10px;
  font-size: 14px;
  margin-right: 10px;
}

.add-experience-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #4f71ff;
  color: #fff;
  border-radius: 20px;
  padding: 12px;
  margin: 25px 0;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
}

.add-experience-btn:active {
  background: #3d5acc;
}
</style>
