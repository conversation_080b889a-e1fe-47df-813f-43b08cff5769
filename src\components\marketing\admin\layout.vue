<template>
  <div class="layout">
    <header v-if="ifShowNavs">
      <Navigation />
    </header>

    <el-container>
      <el-aside width="208px" v-if="ifShowMenus">
        <Menus />
      </el-aside>

      <el-main v-if="ifShowNavs && ifShowMenus">
        <router-view />
      </el-main>
      <router-view v-else />
    </el-container>
  </div>
</template>

<script>
import Menus from 'kit/components/marketing/admin/menus.vue'
import Navigation from 'kit/components/marketing/admin/navigation.vue'

export default {
  components: {
    Menus,
    Navigation
  },
  computed: {
    ifShowNavs() {
      return this.$route.meta.navs
    },
    ifShowMenus() {
      return this.$route.meta.navs
    }
  }
}
</script>
<style scoped>
.layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f2f4f7;
}
.el-container {
  flex: 1;
  overflow: auto;
}
.el-main {
  padding: 0;
  padding-left: 16px;
  padding-top: 16px;
}
</style>
