<template>
  <div style="padding: 80px 0">
    <FeedbackImage
      type="wechatActivityCreatedSuccessfully"
      style="width: 360px; margin: 0 auto"
    />
    <h2 style="margin-bottom: 24px">创建成功</h2>

    <footer>
      <el-button @click="$router.back()">完成</el-button>
      <el-button type="primary" @click="handleCreateActiveClick"
        >创建营销活动</el-button
      >
    </footer>
  </div>
</template>
<script>
import FeedbackImage from 'kit/components/marketing/admin/feedbackImage.vue'

export default {
  components: {
    FeedbackImage
  },
  methods: {
    handleCreateActiveClick() {
      this.$router.replace('/activity/wechatActivitiesNew')
    }
  }
}
</script>
<style scoped>
h2 {
  color: #1e2228ff;
  font-size: 16px;
  font-weight: 600;
  font-family: 'PingFang SC';
  text-align: center;
}
footer {
  display: flex;
  justify-content: center;
}
</style>
