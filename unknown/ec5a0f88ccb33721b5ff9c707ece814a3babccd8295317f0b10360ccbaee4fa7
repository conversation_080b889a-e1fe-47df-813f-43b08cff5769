<template>
    <el-select :value="value" :placeholder="placeholder" v-bind="$attrs" @input="onInput" :clearable="clearable"
        v-on="$listeners">
        <el-option v-for="item in newOptions" :key="item.value" :label="item.label" :value="item.value" v-bind="item" />
    </el-select>
</template>
<script>
export default {
    props: {
        options: {
            type: Array,
            default: () => [],
            require: true
        },
        clearable: {
            type: Boolean,
            default: false
        },
        value: {
            type: [String, Array, Number, Boolean],
            default: '',
            require: true
        },
        placeholder: {
            type: String,
            default: '请选择'
        }
    },
    data() {
        return {
            newOptions: []
        }
    },
    watch: {
        value: 'initOptionItemValueType',
        options: 'initOptionItemValueType'
    },
    created() {
        this.initOptionItemValueType()
    },
    methods: {
        onInput(value) {
            this.$emit('input', value)
        },
        initOptionItemValueType() {
            const map = {
                number: Number,
                string: String,
                boolean: Boolean,
            }

            const tyFn = map[typeof this.value]
            if (!tyFn) {
                return this.newOptions = this.options
            }

            const optionItem = this.newOptions[0]
            if (optionItem?.value === typeof this.value) return

            this.newOptions = this.options.map(item => {
                item.value = tyFn(item.value)
                return item
            })
        }
    }
}
</script>
