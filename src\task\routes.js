import Home from './app.vue'
import Login from 'kit/pages/operateLabor/task/login.vue'
import OcrIdentify from 'kit/pages/operateLabor/task/ocrIdentify.vue'
import FaceAuth from 'kit/pages/operateLabor/task/faceAuth.vue'
import Ocr from 'kit/pages/operateLabor/task/ocr.vue'
import LaborContract from 'kit/pages/operateLabor/task/laborContract.vue'
import ShowContract from 'kit/pages/operateLabor/task/showContract.vue'
import LaborContractSign from 'kit/pages/operateLabor/task/laborContractSign.vue'
import AllAgreement from 'kit/pages/operateLabor/task/allAgreement.vue'
import HomePage from 'kit/pages/operateLabor/task/homePage.vue'
import HomeSearch from 'kit/pages/operateLabor/task/home/<USER>'
import HomeFilter from 'kit/pages/operateLabor/task/home/<USER>'
import MyTask from 'kit/pages/operateLabor/task/myTask.vue'
import MyTaskDetail from 'kit/pages/operateLabor/task/myTaskDetail.vue'
import HomeDetail from 'kit/pages/operateLabor/task/homePageDetail.vue'
import Mine from 'kit/pages/operateLabor/task/mine.vue'
import Receipt from 'kit/pages/operateLabor/task/mine/receipt.vue'
import PersonalProfile from 'kit/pages/operateLabor/task/mine/personalProfile.vue'
import BasicInfo from 'kit/pages/operateLabor/task/mine/personalProfileBasicInfo.vue'
import ProjectExperience from 'kit/pages/operateLabor/task/mine/personalProfileProjectExperience.vue'
import AttachmentInfo from 'kit/pages/operateLabor/task/mine/personalProfileAttachmentInfo.vue'
import BankInfo from 'kit/pages/operateLabor/task/mine/personalProfileBankInfo.vue'
import BankInfoBind from 'kit/pages/operateLabor/task/mine/personalProfileBankInfoBind.vue'
import Setting from 'kit/pages/operateLabor/task/mine/setting.vue'
import LoginPassword from 'kit/pages/operateLabor/task/mine/loginPassword.vue'
import Delivery from 'kit/pages/operateLabor/task/delivery.vue'

const routes = [
  {
    path: '/',
    component: Home
  },
  {
    path: '/login',
    component: Login,
    meta: {
      title: '个人登录'
    }
  },
  {
    path: '/ocr',
    component: Ocr,
    meta: {
      title: '实名认证'
    }
  },
  {
    path: '/ocrIdentify',
    component: OcrIdentify,
    meta: {
      title: '实名认证',
    }
  },
  {
    path: '/faceAuth',
    component: FaceAuth,
    meta: {
      title: '人脸识别'
    }
  },
  {
    path: '/laborContract',
    component: LaborContract,
    meta: {
      title: '授权协议'
    }
  },
  {
    path: '/showContract',
    component: ShowContract,
    meta: {
      title: '授权协议'
    }
  },
  {
    path: '/laborContractSign',
    component: LaborContractSign,
    meta: {
      title: '授权协议'
    }
  },
  {
    path: '/allAgreement',
    component: AllAgreement,
    meta: {
      title: '我的协议'
    }
  },
  {
    path: '/homePage',
    component: HomePage,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/home/<USER>',
    component: HomeSearch,
    meta: {
      title: '首页搜索'
    }
  },
  {
    path: '/home/<USER>',
    component: HomeFilter,
    meta: {
      title: '首页筛选'
    }
  },
  {
    path: '/home/<USER>',
    component: HomeDetail,
    meta: {
      title: '任务详情'
    }
  },
  {
    path: '/myTask',
    component: MyTask,
    meta: {
      title: '任务'
    }
  },
  {
    path: '/myTask/detail',
    component: MyTaskDetail,
    meta: {
      title: '任务详情'
    }
  },
  {
    path: '/delivery',
    component: Delivery,
    meta: {
      title: '交付成果'
    }
  },
  {
    path: '/mine',
    component: Mine,
    meta: {
      title: '我的'
    }
  },
  {
    path: '/receipt',
    component: Receipt,
    meta: {
      title: '收条'
    }
  },
  {
    path: '/personalProfile',
    component: PersonalProfile,
    meta: {
      title: '个人档案'
    }
  },
  {
    path: '/personalProfile/basicInfo',
    component: BasicInfo,
    meta: {
      title: '基本信息'
    }
  },
  {
    path: '/personalProfile/projectExperience',
    component: ProjectExperience,
    meta: {
      title: '项目经历'
    }
  },
  {
    path: '/personalProfile/attachmentInfo',
    component: AttachmentInfo,
    meta: {
      title: '附件信息'
    }
  },
  {
    path: '/personalProfile/bankInfo',
    component: BankInfo,
    meta: {
      title: '银行卡'
    }
  },
  {
    path: '/personalProfile/bankInfoBind',
    component: BankInfoBind,
    meta: {
      title: '银行卡绑定'
    }
  },
  {
    path: '/setting',
    component: Setting,
    meta: {
      title: '设置'
    }
  },
  {
    path: '/loginPassword',
    component: LoginPassword,
    meta: {
      title: '登录密码'
    }
  },
]

export default routes
