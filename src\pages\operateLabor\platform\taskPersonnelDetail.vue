<template>
  <div class="task-personnel-detail-container" v-loading="loading">
    <div class="detail-container">
      <!-- 任务基本信息 -->
      <div class="summary-container">
        <div class="summary-content">
          <div class="summary-left">
            <div class="summary-item">
              <label>任务名称：</label>
              <span>{{ taskInfo.taskName || '-' }}</span>
              <el-tag
                v-if="taskInfo.taskStatus"
                :type="getTaskStatusTagType(taskInfo.taskStatus)"
                size="small"
                effect="light"
                style="margin-left: 8px;"
              >
                {{ getTaskStatusText(taskInfo.taskStatus) }}
              </el-tag>
            </div>
            <div class="summary-item">
              <label>任务可见性：</label>
              <span>{{ taskInfo.taskVisibility || '-' }}</span>
            </div>
            <div class="summary-item">
              <label>所属服务合同：</label>
              <span>{{ taskInfo.businessContractName || '-' }}</span>
            </div>
            <div class="summary-item">
              <label>创建时间：</label>
              <span>{{ formatDateTime(taskInfo.createTime) }}</span>
            </div>
          </div>

          <!-- 右上角按钮 -->
          <!-- <div class="summary-actions">
            <el-button type="primary" size="small" @click="handleTaskQRCode">
              任务二维码
            </el-button>
          </div> -->
        </div>
      </div>

      <!-- 人员列表 -->
      <div class="table-container">
        <!-- 搜索条件 -->
        <div class="search-container">
          <el-form :model="conditions" inline class="search-form">
            <el-form-item>
              <el-input
                v-model="conditions.filters.name"
                placeholder="请输入姓名"
                clearable
                @keyup.enter.native="onSearch"
              />
            </el-form-item>
            <el-form-item>
              <el-input
                v-model="conditions.filters.cellPhone"
                placeholder="请输入手机号"
                clearable
                @keyup.enter.native="onSearch"
              />
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="conditions.filters.laborTaskStatus"
                placeholder="请选择人员任务状态"
                clearable
              >
                <el-option label="待审核" value="REVIEWING" />
                <el-option label="待接受" value="PENDING_ACCEPTANCE" />
                <el-option label="进行中" value="IN_PROGRESS" />
                <el-option label="已完成" value="COMPLETED" />
                <el-option label="已拒绝" value="REJECTED" />
              </el-select>
            </el-form-item>
            <el-form-item style="margin-left: 80px;">
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table
          :data="tableData"
          :height="tableHeight"
          :stripe="false"
          highlight-current-row
          :header-cell-style="{background:'#f5f7fa', color: '#303133', fontWeight: '550'}"
          v-loading="tableLoading"
        >
          <template slot="empty">
            <div class="empty-data">暂无数据</div>
          </template>
          <el-table-column prop="name" label="姓名" min-width="120" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.name || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="phoneNumber" label="手机号" min-width="140">
            <template slot-scope="scope">
              {{ scope.row.phoneNumber || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="claimType" label="认领类型" min-width="120">
            <template slot-scope="scope">
              {{ scope.row.claimType || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="laborTaskStatus" label="人员任务状态" min-width="120">
            <template slot-scope="scope">
              <el-tag
                :type="getLaborTaskStatusTagType(scope.row.laborTaskStatus)"
                size="medium"
                effect="light"
              >
                {{ getLaborTaskStatusText(scope.row.laborTaskStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="deliveryStatus" label="交付状态" min-width="120">
            <template slot-scope="scope">
              {{ scope.row.deliveryStatus || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="laborTaskStartTime" label="任务开始时间" min-width="150">
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.laborTaskStartTime) }}
            </template>
          </el-table-column>
           <el-table-column prop="laborTaskFinishTime" label="任务完成时间" min-width="150">
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.laborTaskFinishTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="sex" label="性别" min-width="80">
            <template slot-scope="scope">
              {{ scope.row.sex || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="age" label="年龄" min-width="80">
            <template slot-scope="scope">
              {{ scope.row.age || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="idCardNumber" label="身份证号" min-width="180" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.idCardNumber || '-' }}
            </template>
          </el-table-column>

          <!-- <el-table-column label="操作" width="320" fixed="right">
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-button
                  v-if="scope.row.laborTaskStatus === '待审核'"
                  type="text"
                  size="small"
                  @click="handleAuditPersonnel(scope.row)"
                >
                  审核人员
                </el-button>
                <el-button
                  v-if="scope.row.laborTaskStatus === '已完成' || scope.row.laborTaskStatus === 'COMPLETED'"
                  type="text"
                  size="small"
                  @click="handleViewDelivery(scope.row)"
                >
                  查看交付
                </el-button>
              </div>
            </template>
          </el-table-column> -->
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            :current-page="Math.floor(conditions.offset / conditions.limit) + 1"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="conditions.limit"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </div>
    </div>

    <!-- 任务二维码对话框 -->
    <el-dialog
      title="任务二维码"
      :visible.sync="qrCodeDialogVisible"
      width="400px"
      :close-on-click-modal="false"
      @close="handleQRCodeDialogClose"
    >
      <div class="qr-code-content">
        <div class="qr-code-container" v-loading="qrCodeLoading">
          <div v-if="qrCodeUrl" class="qr-code-image">
            <img :src="qrCodeUrl" alt="任务二维码" />
          </div>
          <div v-else class="qr-code-placeholder">
            <i class="el-icon-picture"></i>
            <p>二维码生成中...</p>
          </div>
        </div>
        <div class="qr-code-info">
          <p>扫描二维码可快速访问任务详情</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleQRCodeDialogClose">关闭</el-button>
        <el-button type="primary" @click="handleDownloadQRCode">下载二维码</el-button>
      </div>
    </el-dialog>

    <!-- 审核人员对话框 -->
    <el-dialog
      title="审核人员"
      :visible.sync="auditPersonnelDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      @close="cancelAuditPersonnel"
    >
      <el-form :model="auditForm" label-width="100px">
        <el-form-item label="审核结果:">
          <el-radio-group v-model="auditForm.auditResult">
            <el-radio :label="true">审核通过</el-radio>
            <el-radio :label="false">审核拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核说明:">
          <el-input
            v-model="auditForm.auditResultDesc"
            type="textarea"
            :rows="3"
            placeholder="请输入审核说明（可选）"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAuditPersonnel">取消</el-button>
        <el-button type="primary" @click="confirmAuditPersonnel" :loading="auditPersonnelLoading">确定</el-button>
      </div>
    </el-dialog>

    <!-- 查看交付对话框 -->
    <el-dialog
      title="查看交付"
      :visible.sync="deliveryDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      @close="handleDeliveryDialogClose"
    >
      <div class="delivery-content" v-loading="deliveryLoading">
        <div v-if="deliveryData.length > 0">
          <div v-for="(delivery, index) in deliveryData" :key="index" class="delivery-item">
            <div class="delivery-header">
              <h4>交付记录 {{ index + 1 }}</h4>
              <span class="delivery-time">{{ formatDateTime(delivery.createTime) }}</span>
            </div>
            <div class="delivery-body">
              <div class="delivery-info">
                <div class="info-item">
                  <label>交付说明：</label>
                  <span>{{ delivery.description || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>交付状态：</label>
                  <el-tag :type="getDeliveryStatusTagType(delivery.status)" size="small">
                    {{ getDeliveryStatusText(delivery.status) }}
                  </el-tag>
                </div>
              </div>
              <div v-if="delivery.attachments && delivery.attachments.length > 0" class="delivery-attachments">
                <h5>交付附件：</h5>
                <div class="attachment-list">
                  <div v-for="(attachment, attachIndex) in delivery.attachments" :key="attachIndex" class="attachment-item">
                    <i :class="getFileIcon(attachment.fileName)"></i>
                    <span class="attachment-name">{{ attachment.fileName }}</span>
                    <el-button type="text" size="small" @click="handleDownloadAttachment(attachment)">下载</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="no-delivery">
          <i class="el-icon-document"></i>
          <p>暂无交付记录</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDeliveryDialogClose">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 任务完成凭证组件 -->
    <TaskCredentialsDialog
      :visible.sync="credentialsDialogVisible"
      :credentials-data="credentialsData"
      :loading="credentialsLoading"
    />
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'
import makeClient from '../../../services/operateLabor/makeClient'
import { getToken } from '../../../helpers/token'
import TaskCredentialsDialog from './components/TaskCredentialsDialog.vue'

const client = makeClient()

export default {
  components: {
    TaskCredentialsDialog
  },
  data() {
    return {
      loading: true,
      tableLoading: false,
      taskInfo: {},
      tableData: [],
      total: 0,
      tableHeight: 500,
      // 任务二维码相关
      qrCodeDialogVisible: false,
      qrCodeLoading: false,
      qrCodeUrl: '',
      // 审核人员对话框相关
      auditPersonnelDialogVisible: false,
      auditPersonnelLoading: false,
      currentAuditRow: null,
      auditForm: {
        auditResult: true, // 默认选择审核通过
        auditResultDesc: ''
      },
      // 查看交付相关
      deliveryDialogVisible: false,
      deliveryLoading: false,
      deliveryData: [],
      currentDeliveryRow: null,
      // 任务完成凭证相关
      credentialsLoading: false,
      credentialsDialogVisible: false,
      credentialsData: {
        taskName: '',
        taskFinishTime: '',
        taskCredentials: []
      },
      headerToken: {
        Authorization: `Bearer ${getToken()}`
      },
      conditions: {
        offset: 0,
        limit: 10,
        sorts: [],
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          taskId: null,
          name: '',
          cellPhone: '',
          laborTaskStatus: ''
        }
      }
    }
  },
  async created() {
    this.loading = true
    const taskId = this.$route.params.id
    this.conditions.filters.taskId = parseInt(taskId)

    await Promise.all([
      this.loadTaskInfo(),
      this.loadPersonnelList()
    ])
    this.loading = false
    this.setTableHeight()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    async loadTaskInfo() {
      const taskId = this.$route.params.id

      // 使用supplier接口查询任务信息
      const [err, r] = await client.supplierQueryTask({
        body: {
          offset: 0,
          limit: 10,
          sorts: [],
          withTotal: true,
          withDisabled: true,
          withDeleted: true,
          filters: {
            taskName: '',
            taskId: parseInt(taskId),
            taskStatus: '',
            businessContractId: '',
            taskVisibility: '',
            taskTagId: '',
            createTimeStart: null,
            createTimeEnd: null
          }
        }
      })

      if (err) {
        handleError(err)
        return
      }

      if (r.data && r.data.list && r.data.list.length > 0) {
        this.taskInfo = r.data.list[0] || {}
      }
    },

    async loadPersonnelList() {
      this.tableLoading = true

      // 使用supplier接口查询任务人员列表
      const [err, r] = await client.supplierQueryTaskLabor({
        body: this.conditions
      })

      this.tableLoading = false

      if (err) {
        handleError(err)
        return
      }

      this.tableData = r.data.list || []
      this.total = r.data.total || 0

      this.$nextTick(() => {
        this.setTableHeight()
      })
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.loadPersonnelList()
    },

    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0
      this.loadPersonnelList()
    },

    onSearch() {
      this.conditions.offset = 0
      this.loadPersonnelList()
    },

    onReset() {
      this.conditions.filters = {
        taskId: this.conditions.filters.taskId,
        name: '',
        cellPhone: '',
        laborTaskStatus: ''
      }
      this.conditions.offset = 0
      this.loadPersonnelList()
    },

    formatDateTime(value) {
      if (!value) return '-'
      return new Date(value).toLocaleString('zh-CN')
    },

    formatTaskTime(startTime, endTime) {
      if (!startTime || !endTime) return '-'
      const start = new Date(startTime).toLocaleDateString('zh-CN')
      const end = new Date(endTime).toLocaleDateString('zh-CN')
      return `${start} 至 ${end}`
    },

    formatPersonnelRequirement(taskInfo) {
      const requirements = []
      if (taskInfo.requiredGender) {
        requirements.push(taskInfo.requiredGender)
      }
      return requirements.length > 0 ? requirements.join('、') : '-'
    },

    getTaskStatusText(status) {
      const statusMap = {
        'REVIEWING': '待审核',
        'PENDING_ACCEPTANCE': '待接受',
        'IN_PROGRESS': '进行中',
        'COMPLETED': '已完成',
        'REJECTED': '已拒绝',
        // 兼容旧的状态值
        'PENDING': '待接受',
        // 兼容任务状态
        '审核中': '审核中',
        '进行中': '进行中',
        '审核失败': '审核失败',
        '已完成': '已完成',
        '已停用': '已停用'
      }
      return statusMap[status] || status || '-'
    },

    getTaskStatusTagType(status) {
      const typeMap = {
        'REVIEWING': 'warning',
        'PENDING_ACCEPTANCE': 'warning',
        'IN_PROGRESS': 'primary',
        'COMPLETED': 'success',
        'REJECTED': 'danger',
        // 兼容旧的状态值
        'PENDING': 'warning',
        // 兼容任务状态
        '审核中': 'warning',
        '进行中': 'success',
        '审核失败': 'danger',
        '已完成': 'success',
        '已停用': 'info'
      }
      return typeMap[status] || 'info'
    },

    getLaborTaskStatusText(status) {
      const statusMap = {
        'REVIEWING': '待审核',
        'PENDING_ACCEPTANCE': '待接受',
        'IN_PROGRESS': '进行中',
        'COMPLETED': '已完成',
        'REJECTED': '已拒绝'
      }
      return statusMap[status] || status || '-'
    },

    getLaborTaskStatusTagType(status) {
      const typeMap = {
        '待接受': 'warning',
        '审核中': 'warning',
        '进行中': 'success',
        '审核失败': 'danger',
        '已完成': 'success',
        '已停用': 'info'
      }
      return typeMap[status] || 'info'
    },

    getDeliveryStatusText(status) {
      const statusMap = {
        'PENDING': '待交付',
        'DELIVERED': '已交付',
        'ACCEPTED': '已验收',
        'REJECTED': '已拒绝'
      }
      return statusMap[status] || status || '-'
    },

    getDeliveryStatusTagType(status) {
      const typeMap = {
        'PENDING': 'warning',
        'DELIVERED': 'primary',
        'ACCEPTED': 'success',
        'REJECTED': 'danger'
      }
      return typeMap[status] || 'info'
    },

    getFileIcon(fileName) {
      if (!fileName) return 'el-icon-document'

      const ext = fileName.toLowerCase().split('.').pop()
      const iconMap = {
        // 图片
        'jpg': 'el-icon-picture',
        'jpeg': 'el-icon-picture',
        'png': 'el-icon-picture',
        'gif': 'el-icon-picture',
        'bmp': 'el-icon-picture',
        'webp': 'el-icon-picture',
        // 文档
        'pdf': 'el-icon-document',
        'doc': 'el-icon-document',
        'docx': 'el-icon-document',
        'xls': 'el-icon-document',
        'xlsx': 'el-icon-document',
        'ppt': 'el-icon-document',
        'pptx': 'el-icon-document',
        'txt': 'el-icon-document',
        // 压缩包
        'zip': 'el-icon-folder',
        'rar': 'el-icon-folder',
        '7z': 'el-icon-folder',
        // 视频
        'mp4': 'el-icon-video-camera',
        'avi': 'el-icon-video-camera',
        'mov': 'el-icon-video-camera',
        'wmv': 'el-icon-video-camera'
      }

      return iconMap[ext] || 'el-icon-document'
    },

    // 任务二维码
    async handleTaskQRCode() {
      this.$message.info('任务二维码功能待实现')
      // this.qrCodeDialogVisible = true
      // this.qrCodeLoading = true
      // this.qrCodeUrl = ''

      // try {
      //   const taskId = this.$route.params.id
      //   // 这里应该调用生成二维码的API
      //   // 暂时使用模拟数据
      //   setTimeout(() => {
      //     this.qrCodeUrl = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`
      //     this.qrCodeLoading = false
      //   }, 1000)
      // } catch (error) {
      //   console.error('生成二维码失败：', error)
      //   this.$message.error('生成二维码失败，请重试')
      //   this.qrCodeLoading = false
      // }
    },

    handleQRCodeDialogClose() {
      this.qrCodeDialogVisible = false
      this.qrCodeUrl = ''
    },

    handleDownloadQRCode() {
      if (!this.qrCodeUrl) {
        this.$message.warning('二维码还未生成完成')
        return
      }

      // 创建下载链接
      const a = document.createElement('a')
      a.href = this.qrCodeUrl
      a.download = `任务二维码_${this.taskInfo.taskName || 'task'}.png`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    },

    // 审核人员
    handleAuditPersonnel(row) {
      this.currentAuditRow = row
      this.auditForm.auditResult = true
      this.auditForm.auditResultDesc = ''
      this.auditPersonnelDialogVisible = true
    },

    async confirmAuditPersonnel() {
      if (!this.currentAuditRow) return

      this.auditPersonnelLoading = true

      try {
        // 使用supplier接口审核人员
        const [err] = await client.supplierAuditTaskLabor({
          body: {
            taskId: this.taskInfo.taskId,
            laborId: this.currentAuditRow.laborId,
            auditResult: this.auditForm.auditResult,
            auditResultDesc: this.auditForm.auditResultDesc
          }
        })

        if (err) {
          handleError(err)
          return
        }

        handleSuccess('审核成功')
        this.auditPersonnelDialogVisible = false
        this.loadPersonnelList()
      } catch (error) {
        console.error('审核失败：', error)
        this.$message.error('审核失败，请重试')
      } finally {
        this.auditPersonnelLoading = false
      }
    },

    cancelAuditPersonnel() {
      this.auditPersonnelDialogVisible = false
      this.currentAuditRow = null
      this.auditForm = {
        auditResult: true,
        auditResultDesc: ''
      }
    },

    // 查看交付
    async handleViewDelivery(row) {
      this.$message.info('查看交付功能待实现')
      // this.currentDeliveryRow = row
      // this.deliveryDialogVisible = true
      // this.deliveryLoading = true
      // this.deliveryData = []

      // try {
      //   // 这里应该调用查看交付的API
      //   // 暂时使用模拟数据
      //   setTimeout(() => {
      //     this.deliveryData = [
      //       {
      //         id: 1,
      //         description: '任务交付说明',
      //         status: 'DELIVERED',
      //         createTime: new Date().toISOString(),
      //         attachments: [
      //           {
      //             fileName: '交付文档.pdf',
      //             fileUrl: '#'
      //           }
      //         ]
      //       }
      //     ]
      //     this.deliveryLoading = false
      //   }, 1000)
      // } catch (error) {
      //   console.error('获取交付信息失败：', error)
      //   this.$message.error('获取交付信息失败，请重试')
      //   this.deliveryLoading = false
      // }
    },

    handleDeliveryDialogClose() {
      this.deliveryDialogVisible = false
      this.currentDeliveryRow = null
      this.deliveryData = []
    },

    handleDownloadAttachment(attachment) {
      // 这里应该实现附件下载逻辑
      this.$message.info('下载功能待实现')
    },



    handleResize() {
      this.setTableHeight()
    },

    setTableHeight() {
      try {
        const windowHeight = window.innerHeight
        const summaryHeight = this.$el?.querySelector('.summary-container')?.offsetHeight || 0
        const searchHeight = this.$el?.querySelector('.search-container')?.offsetHeight || 0
        const tableHeaderHeight = this.$el?.querySelector('.table-header')?.offsetHeight || 0
        const paginationHeight = this.$el?.querySelector('.pagination-container')?.offsetHeight || 40
        const padding = 60

        const availableHeight = windowHeight - summaryHeight - searchHeight - tableHeaderHeight - paginationHeight - padding

        this.tableHeight = Math.max(300, Math.min(availableHeight, 600))
      } catch (error) {
        console.warn('设置表格高度时出错:', error)
        this.tableHeight = 500
      }
    }
  }
}
</script>

<style scoped>
.task-personnel-detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: #f5f7fa;
  padding: 12px;
  box-sizing: border-box;
}

.detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 任务基本信息样式 */
.summary-container {
  background: #fff;
  padding: 12px 20px;
  margin-bottom: 10px;
  border-radius: 8px;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 0 0 auto;
}

.summary-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.summary-left {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 8px 24px;
}

.summary-item {
  line-height: 28px;
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
  min-width: 200px;
}

.summary-item label {
  display: inline-block;
  width: 100px;
  text-align: right;
  color: #909399;
  margin-right: 8px;
  flex-shrink: 0;
}

.summary-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  margin-left: 20px;
}

/* 搜索条件样式 */
.search-container {
  margin-bottom: 8px;
  background: #fff;
  padding: 5px;
  border-radius: 8px;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.search-form .el-form-item {
  margin: 0;
}

.search-form .el-form-item .el-form-item__content {
  width: 200px;
}

/* 表格容器样式 */
.table-container {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-bottom: 12px;
}

.table-actions .el-button {
  border-radius: 4px;
  padding: 9px 20px;
  transition: all 0.3s;
}

.table-actions .el-button--primary {
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.table-actions .el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.table-actions .el-button--success {
  box-shadow: 0 2px 6px rgba(103, 194, 58, 0.2);
}

.table-actions .el-button--success:hover {
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
  transform: translateY(-1px);
}

/* 表格样式 */
.el-table {
  margin-bottom: 5px;
  border: none;
}

.el-table th,
.el-table--medium th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 550;
  text-align: left;
  padding: 4px 0;
  border-bottom: 1px solid #EBEEF5;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.el-table td,
.el-table--medium td {
  padding: 4px 0;
  color: #606266;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 3px;
  transition: all 0.2s;
  min-width: 48px;
  height: 24px;
  line-height: 16px;
}

.action-buttons .el-button--text {
  background: transparent;
  border: none;
  padding: 4px 8px;
}

.action-buttons .el-button--text:hover {
  background: #ecf5ff;
  transform: translateY(-1px);
}

.empty-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
  width: 100%;
}

.pagination-container {
  padding: 12px 0 0;
  text-align: right;
  background: #fff;
  margin-top: auto;
}

/* 二维码对话框样式 */
.qr-code-content {
  text-align: center;
  padding: 20px 0;
}

.qr-code-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  margin-bottom: 20px;
}

.qr-code-image img {
  max-width: 200px;
  max-height: 200px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.qr-code-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #909399;
}

.qr-code-placeholder i {
  font-size: 48px;
  margin-bottom: 12px;
}

.qr-code-info {
  color: #606266;
  font-size: 14px;
}

/* 交付对话框样式 */
.delivery-content {
  max-height: 500px;
  overflow-y: auto;
}

.delivery-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 16px;
  overflow: hidden;
}

.delivery-header {
  background: #f5f7fa;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ebeef5;
}

.delivery-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.delivery-time {
  font-size: 12px;
  color: #909399;
}

.delivery-body {
  padding: 16px;
}

.delivery-info {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-item label {
  font-weight: 600;
  color: #606266;
  min-width: 80px;
  margin-right: 8px;
}

.info-item span {
  color: #303133;
  flex: 1;
}

.delivery-attachments h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f9f9f9;
  border-radius: 4px;
  gap: 8px;
}

.attachment-item i {
  font-size: 16px;
  color: #409eff;
}

.attachment-name {
  flex: 1;
  font-size: 13px;
  color: #606266;
}

.no-delivery {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.no-delivery i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.no-delivery p {
  margin: 0;
  font-size: 14px;
}

/* 对话框样式 */
.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .summary-content {
    flex-direction: column;
    gap: 16px;
  }

  .summary-left {
    flex-direction: column;
    gap: 8px;
  }

  .summary-item {
    min-width: auto;
  }

  .summary-item label {
    width: 70px;
  }

  .summary-actions {
    margin-left: 0;
    justify-content: flex-start;
  }

  .search-form .el-form-item .el-form-item__content {
    width: 160px;
  }

  .table-actions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .table-actions .el-button {
    width: 100%;
  }
}

@media screen and (max-width: 480px) {
  .task-personnel-detail-container {
    padding: 8px;
  }

  .summary-container,
  .search-container,
  .table-container {
    padding: 12px;
  }

  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 8px;
  }

  .summary-item label {
    min-width: auto;
    margin-bottom: 4px;
  }

  .search-form .el-form-item .el-form-item__content {
    width: 120px;
  }
}
</style>
