<template>
  <div class="homePage">
    <div style="background: #fff; padding: 0 20px">
      <Swipe :autoplay="3000">
        <SwipeItem v-for="(item, index) in images" :key="index">
          <img style="width: 100%; height: 113px" :src="item.imgUrl" />
        </SwipeItem>
      </Swipe>
      <div
        style="
        display: flex;
        flex-wrap: wrap;
        max-height: 180px
        overflow: hidden;
        background: #FFFFFF;
        margin-top: 20px;
      "
      >
        <div
          style="
            flex: 0 0 20%;
            height: 62px;
            box-sizing: border-box;
            text-align: center;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 16px;
          "
          v-for="(item, index) in tagOptions"
          :key="index"
          @click="handleSelected(item.taskTagId)"
        >
          <img
            :src="previewImg(item.fileId)"
            style="width: 40px; height: 40px; object-fit: contain"
            alt=""
          />
          <div class="tags-name">{{ item.taskTagName }}</div>
        </div>
        <div
          style="
            flex: 0 0 20%;
            display: flex;
            flex-direction: column;
            align-items: center;
          "
          @click="handleFilter"
        >
          <img
            style="width: 40px; height: 40px"
            src="../../../assets/images/more2.png"
            alt=""
          />
          <div class="tags-name">更多</div>
        </div>
      </div>
      <div style="margin-top: 30px">
        <div style="font-size: 18px; font-weight: 600">推荐职位</div>
        <div style="display: flex; justify-content: space-between">
          <div class="type-bar">
            <span
              :class="{ active: currType === 'recommend' }"
              @click="handleChangeType('recommend')"
              >推荐给我</span
            >
            <span
              :class="{ active: currType === 'assign' }"
              @click="handleChangeType('assign')"
              >指派给我</span
            >
          </div>
          <div
            class="search-area"
            style="display: flex; align-items: center; margin-top: 15px"
          >
            <div class="search-con" @click="handleSearch">
              <img src="../../../assets/images/weixin/base-find-search.png" />

              <span>搜索</span>
            </div>
            <div
              class="search-con"
              @click="handleFilter"
              :style="{
                background: taskTagName ? '#E2E8FF' : 'transparent',
                color: taskTagName ? '#4F71FF' : ''
              }"
            >
              <img
                v-if="!taskTagName"
                src="../../../assets/images/weixin/edit-filter.png"
              />
              <img
                v-else
                src="../../../assets/images/weixin/edit-filter-active.png"
              />
              <div
                style="
                  max-width: 35px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                "
              >
                {{ taskTagName || '筛选' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div style="box-sizing: border-box; padding: 20px 15px 10px 15px">
      <div v-if="dataList.length">
        <div
          class="task"
          v-for="(item, index) in dataList"
          :key="index"
          @click="handleClick(item)"
        >
          <div
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            "
          >
            <div style="display: flex; flex-direction: column">
              <span
                style="
                  width: 200px;
                  font-size: 16px;
                  font-weight: 600;
                  color: #262935;
                  margin-top: 7px;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                  text-overflow: ellipsis;
                "
                >{{ item.taskName }}</span
              >
            </div>
            <div style="font-size: 16px; font-weight: 600; color: #4f71ff">
              {{ formatterPrice(item.taskAmount) }}
              <span style="font-size: 10px" v-if="item.taskAmount !== '面议'"
                >元</span
              >
            </div>
          </div>
          <div
            style="
              width: 200px;
              white-space: pre-wrap;
              font-size: 13px;
              color: #71788f;
              margin-top: 10px;
            "
          >
            {{ item.enterpriseName || '-' }}
          </div>
          <div
            style="
              display: inline-block;
              margin-top: 10px;
              padding: 4px 8px;
              background: #e2e8ff;
              border-radius: 4px;
            "
          >
            <span style="font-size: 12px; color: #4f71ff">{{
              item.taskTagName
            }}</span>
          </div>
          <div style="height: 1px; background: #f0f2f7; margin-top: 20px"></div>
          <div
            style="
              margin-top: 10px;
              display: flex;
              justify-content: space-between;
            "
          >
            <span style="font-size: 13px; color: #71788f; margin-top: 8px"
              >{{ formatterTime(item.taskStartTime) }} -
              {{ formatterTime(item.taskEndTime) }}</span
            >
            <div class="claim-btn">立即认领</div>
          </div>
        </div>
      </div>
      <div class="no-content" v-else>
        <img
          style="width: 40%"
          src="https://olading-static-resource.oss-cn-beijing.aliyuncs.com/olading-mini-image/olading-front-h5/no-data.png"
        />
        <div style="text-align: center; margin-top: 10px">暂无任务</div>
      </div>
    </div>
  </div>
</template>
<script>
import { Swipe, SwipeItem, Button } from 'vant'
import Tabs from 'kit/components/marketing/admin/tabs.vue'
import handleError from '../../../helpers/handleErrorH5'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Swipe,
    SwipeItem,
    Button,
    Tabs
  },
  data() {
    return {
      images: [],
      tagOptions: [],
      dataList: [],
      currType: 'recommend',
      taskTagId: '',
      taskName: ''
    }
  },
  async created() {
    this.taskTagName = this.$route.query.taskTagName || ''
    var domain = window.location.host
    if (domain.includes('localhost')) {
      domain = '156-dev.olading.com'
    }
    const [err, r] = await client.getDomainInfo({
      body: {
        domain: domain
      }
    })
    if (err) {
      handleError(err)
      return
    }

    console.log('r===', r.data)

    if (r.data?.banner?.length) {
      for (var c of r.data.banner) {
        this.images.push({
          imgUrl: `${window.env?.apiPath}/api/public/previewFile/${c}`
        })
      }
    }
    this.taskTagId = this.$route.query.taskTagId || ''
    this.taskName = this.$route.query.taskName || ''
    await this.queryTaskTag()
    await this.fetchList()
  },
  methods: {
    formatterPrice(value) {
      if (value.includes('-')) {
        return `${value.split('-')[0]}+`.split(' ').join('')
      } else if (value === '面议') {
        return '面议'
      } else {
        return value
      }
    },
    formatterTime(time) {
      if (!time) return ''
      return time.slice(0, 10)
    },
    async queryTaskTag() {
      const [err, r] = await client.queryPublicTaskTag()
      if (err) return handleError(err)
      this.tagOptions = r.data.slice(0, 9)
    },
    async fetchList() {
      const [err, r] = await client.personalQueryTask({
        body: {
          offset: 0,
          limit: 10000,
          withTotal: true,
          withDisabled: true,
          withDeleted: true,
          filters: {
            taskName: this.taskName,
            taskStatus: 'DOING',
            taskTagId: this.taskTagId,
            taskVisible: this.currType === 'recommend' ? 'ALL' : '',
            claimType: this.currType === 'recommend' ? '' : 'ASSIGNED_CLAIM'
          }
        }
      })
      if (err) return handleError(err)
      this.dataList = r.data.list
    },
    handleSelected(id) {
      this.taskTagId = id
      this.taskTagName = this.tagOptions.find(
        item => item.taskTagId === id
      )?.taskTagName
      this.fetchList()
    },
    previewImg(fileId) {
      return `${window.env?.apiPath}/api/public/previewFile/${fileId}`
    },
    handleChangeType(type) {
      this.currType = type
      this.fetchList()
    },
    handleSearch() {
      this.$router.push({
        path: '/home/<USER>',
        query: {
          currType: this.currType
        }
      })
    },
    handleFilter() {
      this.$router.push({
        path: '/home/<USER>',
        query: {
          taskTagId: this.taskTagId || ''
        }
      })
    },
    handleClick(item) {
      this.$router.push({
        path: '/home/<USER>',
        query: {
          id: item.taskId,
          type: this.currType
        }
      })
    }
  }
}
</script>
<style scoped>
.homePage {
  height: 100vh;
  overflow: auto;
  background: #f3f4f7;
}
.tags-name {
  width: 58px;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  color: #262935;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.search-area img {
  width: 16px;
  margin-right: 4px;
}
.search-con {
  display: flex;
  font-size: 12px;
  font-weight: 400;
  color: rgba(38, 41, 53, 1);
  margin-right: 8px;
  padding: 4px 8px;
  border-radius: 4px;
}
.type-bar {
  font-size: 14px;
  margin-top: 20px;
  height: 26px;
}
.type-bar span {
  text-align: center;
  margin-right: 23px;
  padding: 6px 0;
}
.active {
  border-bottom: 2px solid #0082ff;
  color: #0082ff;
  font-weight: 600;
  padding: 6px 0;
}
.task {
  padding: 15px;
  background: #fff;
  margin-bottom: 10px;
  position: relative;
  border-radius: 4px;
}
.claim-btn {
  width: 72px;
  height: 26px;
  background: #4f71ff;
  border-radius: 4px;
  font-weight: 600;
  font-size: 12px;
  color: #ffffff;
  line-height: 26px;
  text-align: center;
}
.no-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
