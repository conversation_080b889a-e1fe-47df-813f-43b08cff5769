const isFunction = obj => {
  return (
    Object.prototype.toString.call(obj) === '[object Function]' ||
    Object.prototype.toString.call(obj) === '[object AsyncFunction]'
  )
}
const isObject = obj => {
  return Object.prototype.toString.call(obj) === '[object Object]'
}

class HTTPClient {
  constructor() {
    this.globalRequestInterceptors = []
    this.globalResponseInterceptors = []
    this.defaultTimeout = 60000 // 默认超时时间 60s
  }

  attachGlobalRequestInterceptor(interceptor) {
    if (!isFunction(interceptor)) {
      throw new Error('interceptor must be a function')
    }
    this.globalRequestInterceptors.push(interceptor)
  }

  attachGlobalResponseInterceptor(interceptor) {
    if (!isFunction(interceptor)) {
      throw new Error('interceptor must be a function')
    }
    this.globalResponseInterceptors.push(interceptor)
  }

  async request(resource, options = {}) {
    if (!resource) {
      throw new Error('resource (url) is required')
    }

    // 1. 应用请求拦截器
    for (const interceptor of this.globalRequestInterceptors) {
      const [newErr, newResource, newOptions] = await interceptor(
        resource,
        options
      )
      if (newErr) return [newErr, null]
      if (newResource) resource = newResource
      if (newOptions && isObject(newOptions)) options = newOptions
    }

    // 2. 核心请求逻辑 (wx.request 封装为 Promise)
    const requestPromise = new Promise((resolve, reject) => {
      wx.request({
        url: resource,
        method: options.method || 'GET',
        data: options.body || {},
        header: options.headers || { 'Content-Type': 'application/json' },
        timeout: options.timeout || this.defaultTimeout,

        success: res => {
          resolve(res)
        },
        fail: err => {
          reject({
            errorCode: 503,
            message: '网络错误，请稍后重试',
            originalError: err
          })
        }
      })
    })

    // 3. 执行请求并捕获错误
    let err, result
    try {
      result = await requestPromise
    } catch (e) {
      err = e
    }

    if (err) {
      return [err, null]
    }

    // 4. 应用响应拦截器
    for (const interceptor of this.globalResponseInterceptors) {
      const [newErr, newResult] = await interceptor(resource, options, result)
      if (newErr) return [newErr, null]
      result = newResult
    }

    return [null, result]
  }
}

// 导出 HTTPClient 的单例
export default HTTPClient
