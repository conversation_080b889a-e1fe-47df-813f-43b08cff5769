<template>
  <div
    class="navigations"
    style="
      display: flex;
      height: 64px;
      overflow: hidden;
      border-bottom: 1px solid #eee;
      border-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    "
  >
    <div
      style="
        display: flex;
        align-items: center;
        gap: 5px;
        flex: 0 0 150px;
        padding: 14px;
      "
    >
      <img :src="logo" style="objectFit: contain; height: 36px" width="150px" />
    </div>
    <div
      class="item"
      :class="{ active: activeIndex === index }"
      v-for="(item, index) in navigations"
      :key="index"
      @click="handleNavClick(item, index)"
    >
      {{ item.title }}
    </div>
    <div
      class="user-area"
      style="
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding-right: 24px;
      "
    >
      <!-- 客户切换器 -->
      <div
        class="customer-switcher"
        style="cursor: pointer; margin-right: 50px;"
        v-if="currentCustomer"
      >
        <el-popover  placement="bottom-start" trigger="click" width="280px" popper-class="customer-switcher-popper">
          <template #reference>
            <div class="customer-switcher-trigger">
              <span class="customer-name">{{ currentCustomer.name }}</span>
              <i class="el-icon-sort" style="transform: rotate(90deg); font-size: 12px; margin-left: 8px; flex-shrink: 0;"></i>
            </div>
          </template>
          <div
            style="
              width: 100%;
              display: flex;
              flex-direction: column;
              max-height: 300px;
              overflow-y: auto;
            "
          >
            <div
              class="customer-item"
              v-for="customer in availableCustomers"
              :key="customer.id"
              style="
                margin-bottom: 10px;
                padding: 8px;
                display: flex;
                align-items: center;
                justify-content: space-between;
              "
              :class="{
                active: customer.id === currentCustomer.id,
              }"
              @click="
                customer.id === currentCustomer.id
                  ? void 0
                  : switchCustomer(customer)
              "
            >
              <span>{{ customer.name }}</span>
              <i
                class="el-icon-check"
                v-if="customer.id === currentCustomer.id"
                style="font-size: 12px; color: var(--o-primary-color)"
              />
            </div>
          </div>
        </el-popover>
      </div>
      <el-dropdown
        @command="handleCommand"
        trigger="click"
        style="cursor: pointer"
      >
        <div class="user-avatar">
          <el-avatar :size="32" icon="el-icon-user-solid"></el-avatar>
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="accountSettings">
            <span>账户管理</span>
            <i
              class="el-icon-arrow-right"
              style="margin-left: 8px; color: #909399"
            ></i>
          </el-dropdown-item>
          <el-dropdown-item divided command="logout">
            <span>退出登录</span>
            <i
              class="el-icon-arrow-right"
              style="margin-left: 8px; color: #909399"
            ></i>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { removeToken, setToken } from 'kit/helpers/token'
import { clearDomainRelatedData, setCustomerUserProfile } from 'kit/pages/operateLabor/merchant/context'
import makeClient from 'kit/services/operateLabor/makeClient'
import handleError from 'kit/helpers/handleError'
import operateLaborLogo from './operateLaborLogo.png'

const client = makeClient()

export default {
  props: {
    logoURL: String,
    navigations: {
      type: Array,
      default: () => []
    },
    availableCustomers: {
      type: Array,
      default: () => []
    },
    currentCustomer: {
      type: Object,
      default: null
    }
  },
  computed: {
    logo() {
      const src = this.logoURL || operateLaborLogo
      if (src.includes('http')) {
        return src
      }
      return `${window.env?.apiPath}/api/public/previewFile/${src}`
    }
  },
  data() {
    return {
      activeIndex: 0
    }
  },
  watch: {
    navigations: {
      immediate: true,
      handler(newNavs) {
        if (!newNavs || newNavs.length === 0) return

        const storedIndexStr = sessionStorage.getItem('mainNavActiveIndex')
        if (storedIndexStr) {
          const storedIndex = parseInt(storedIndexStr, 10)
          if (storedIndex >= 0 && storedIndex < newNavs.length) {
            this.activeIndex = storedIndex
            this.$emit('change', newNavs[storedIndex])
            return // Priority given to session storage
          }
        }

        // Fallback: if no valid index in session storage, use the route.
        this.updateActiveIndexFromRoute()
      }
    },
    '$route.path'() {
      // When route changes, we need to update the active tab.
      this.updateActiveIndexFromRoute()
    }
  },
  methods: {
    handleNavClick(item, index) {
      this.activeIndex = index
      sessionStorage.setItem('mainNavActiveIndex', index)
      this.$emit('change', item)
    },
    updateActiveIndexFromRoute() {
      if (!this.navigations || this.navigations.length === 0) return

      const currentPath = this.$route.path
      const activeNavIndex = this.navigations.findIndex(
        nav =>
          nav.children &&
          nav.children.some(
            child => {
              if (child.path === '/todo') return false
              // 使用更精确的匹配逻辑：当前路径以菜单路径开头，且下一个字符是 '/' 或者是路径结尾
              if (currentPath === child.path) return true
              return currentPath.startsWith(child.path + '/')
            }
          )
      )

      if (activeNavIndex !== -1 && this.activeIndex !== activeNavIndex) {
        this.activeIndex = activeNavIndex
        // When navigating between pages, we should update the session storage as well.
        sessionStorage.setItem('mainNavActiveIndex', activeNavIndex)
        this.$emit('change', this.navigations[activeNavIndex])
      }
    },
    async switchCustomer(customer) {
      try {
        // 显示加载状态
        this.$loading = this.$loading({
          lock: true,
          text: '切换客户中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        // 调用renewToken接口获取新token
        const [err, r] = await client.renewToken({
          body: {
            customerId: customer.id
          }
        })

        if (err) {
          handleError(err)
          return
        }

        // 设置新token
        setToken(r.data.token)

        // 获取新的用户信息
        const [err2, r2] = await client.customerProfile()
        if (err2) {
          handleError(err2)
          return
        }

        // 更新用户信息
        setCustomerUserProfile(r2.data)

        // 更新localStorage中的当前客户信息
        localStorage.setItem('currentCustomer', JSON.stringify(customer))

        // 清除导航状态
        sessionStorage.removeItem('mainNavActiveIndex')

        // 通知父组件切换客户
        this.$emit('customerChanged', customer)

        // 刷新页面
        window.location.reload()
      } catch (error) {
        console.error('切换客户失败:', error)
        this.$message.error('切换客户失败，请重试')
      } finally {
        if (this.$loading) {
          this.$loading.close()
        }
      }
    },
    handleCommand(command) {
      if (command === 'accountSettings') {
        this.$router.push('/accountSettings')
      } else if (command === 'logout') {
        removeToken()
        clearDomainRelatedData()
        this.$router.push('/login')
      }
    }
  }
}
</script>

<style scoped>
.item {
  flex: 0 0 100px;
  height: 48px;
  padding: 0 10px;
  cursor: pointer;
  margin: 8px 4px;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
  font-weight: 500;
  color: #606266;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item:hover {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(79, 172, 254, 0.08) 100%);
  color: var(--o-primary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.15);
}

.item.active {
  color: var(--o-primary-color);
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(79, 172, 254, 0.15) 100%);
  font-weight: 550;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.item.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.1), transparent);
  transition: left 0.8s;
}

.item.active:hover::before {
  left: 100%;
}



/* 客户切换器样式 */
.customer-switcher {
  display: flex;
  align-items: center;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.customer-switcher:hover {
  background: var(--o-primary-bg-color);
}

.customer-switcher-trigger {
  display: flex;
  align-items: center;
  font-size: 15px;
  font-weight: 500;
  color: #303333;
  max-width: 150px;
}

.customer-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
}

.customer-item {
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.customer-item:hover {
  background: var(--o-primary-bg-color);
}

.customer-item.active {
  color: var(--o-primary-color);
  background: var(--o-primary-bg-color);
  font-weight: 500;
}

.customer-item:last-child {
  margin-bottom: 0 !important;
}

</style>
