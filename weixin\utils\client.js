class Client {
  constructor(httpClient) {
    if (!httpClient) {
      throw new Error('httpClient is required')
    }

    this.httpClient = httpClient
  }

  // 图形验证码
  async createCaptcha(options = {}) {
    const resource = `/api/public/createCaptcha`
    return this.httpClient.request(resource, options)
  }

  // 发送登录验证码
  async apiSendLoginSms(options = {}) {
    const resource = `/api/public/sendLoginSms`
    return this.httpClient.request(resource, options)
  }

  // 发送找回密码短信
  async apiSendResetPasswordSms(options = {}) {
    const resource = `/api/public/sendResetPasswordSms`
    return this.httpClient.request(resource, options)
  }

  // 找回用户登录密码
  async apiResetPassword(options = {}) {
    const resource = `/api/public/resetPassword`
    return this.httpClient.request(resource, options)
  }

  // 登录
  async login(options = {}) {
    const resource = `/api/public/login`
    return this.httpClient.request(resource, options)
  }
  // 域名获取品牌信息
  async domainInfo(options = {}) {
    const resource = `/api/public/domainInfo`
    return this.httpClient.request(resource, options)
  }
}

export default Client
