<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>PDF 文档</title>
    <style>
        html,
        body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            background-color: #f0f0f0;
            font-family: sans-serif;
            overflow: hidden;
        }

        #viewer-container {
            width: 100%;
            height: 100%;
            overflow: auto;
            -webkit-overflow-scrolling: touch;
            text-align: center;
        }

        .pdf-page-canvas {
            display: block;
            margin: 20px auto;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            /* --- CSS 修复 --- */
            width: 100%;
            height: auto;
        }

        #loader {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 20px 30px;
            border-radius: 10px;
            font-size: 16px;
            z-index: 2000;
        }
    </style>
</head>

<body style="padding: 0;margin: 0;">
    <div id="viewer-container">
        <!-- PDF 页面将动态插入到这里 -->
    </div>

    <div id="loader">正在加载文档...</div>

    <!-- 确保路径正确 -->
    <script src="/js/pdf.mjs" type="module"></script>

    <script type="module">
        import * as pdfjsLib from '/js/pdf.mjs';

        // --- 1. 配置 ---
        pdfjsLib.GlobalWorkerOptions.workerSrc = '/js/pdf.worker.mjs';

        // --- 2. 获取 DOM 元素 ---
        const viewerContainer = document.getElementById('viewer-container');
        const loader = document.getElementById('loader');

        // --- 4. 核心渲染函数 (渲染所有页面) ---
        async function renderAllPages() {
            // --- JavaScript 修复 A: 获取设备像素比 ---
            const devicePixelRatio = window.devicePixelRatio || 1;
            const pdfDoc = this; // 从调用上下文中获取 pdfDoc

            for (let pageNum = 1; pageNum <= pdfDoc.numPages; pageNum++) {
                try {
                    const canvas = document.createElement('canvas');
                    canvas.className = 'pdf-page-canvas';
                    viewerContainer.appendChild(canvas);
                    const ctx = canvas.getContext('2d');

                    const page = await pdfDoc.getPage(pageNum);
                    const pageWidth = page.getViewport({ scale: 1 }).width;

                    // --- JavaScript 修复 B: 在计算缩放值时乘以 devicePixelRatio ---
                    const dynamicScale = (viewerContainer.clientWidth / pageWidth) * devicePixelRatio;

                    const viewport = page.getViewport({ scale: dynamicScale });

                    canvas.height = viewport.height;
                    canvas.width = viewport.width;

                    const renderContext = {
                        canvasContext: ctx,
                        viewport: viewport,
                    };

                    await page.render(renderContext).promise;

                } catch (error) {
                    console.error(`渲染第 ${pageNum} 页时出错:`, error);
                }
            }
        }

        // --- 5. 初始化和加载 ---
        async function loadDocument(url) {
            if (!url) {
                loader.textContent = "错误: 未提供文件URL。";
                return;
            }

            try {
                const loadingTask = pdfjsLib.getDocument(url);
                const pdfDoc = await loadingTask.promise; // 将pdfDoc作为局部变量
                loader.textContent = `加载完成，共 ${pdfDoc.numPages} 页。正在渲染...`;

                // 使用 .call 来传递正确的 `this` 上下文 (pdfDoc)
                await renderAllPages.call(pdfDoc);

            } catch (error) {
                alert(error)
                console.error("加载PDF失败:", error);
                loader.textContent = `加载失败: ${error.message}`;
            } finally {
                loader.style.display = 'none';
            }
        }

        // 从URL的查询参数中获取文件地址
        const params = new URLSearchParams(window.location.search);
        // 您之前的代码使用了 `pdf` 作为参数名，这里保持一致
        const pdfUrl = params.get('pdf');

        // 启动加载
        loadDocument(pdfUrl);

    </script>
</body>

</html>