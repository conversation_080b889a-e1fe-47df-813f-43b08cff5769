<template>
  <div
    class="bankInfoBind"
    style="
      height: 100vh;
      background: #f7fbfd;
      box-sizing: border-box;
    "
  >
    <NavBar left-text="返回" left-arrow @click-left="onClickLeft" />
    <div style="padding: 50px 20px 0">
      <Form ref="form">
      <Field v-model="params.name" label="持卡人" readonly />
      <Field
        v-model="params.bankCardNo"
        name="bankCardNo"
        label="银行卡号"
        placeholder="请输入本人银行卡号"
        type="digit"
        :rules="[{ required: true, message: '' }, { pattern: /^[1-9]\d{11,19}$/, message: '请输入正确的12-20位银行卡号' }]"
      />
      <Field
        v-model="params.bankName"
        label="开户支行名称"
        placeholder="请输入开户支行"
      />
    </Form>
    <Button class="btn" @click="confirm" :loading="loading">确 认</Button>
    <div style="color: #707b82; font-size: 14px">
      <p>注：</p>
      <div>1、请绑定银行的一类卡，二类卡会有限额</div>
      <div>2、请绑定持卡人本人的银行卡</div>
      <div>3、建议使用工、农、中、建、招等大行的银行卡</div>
    </div>
    </div>
  </div>
</template>
<script>
import { Form, Field, Button, NavBar } from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Form,
    Field,
    Button,
    NavBar
  },
  data() {
    return {
      params: {
        name: '',
        bankCardNo: '',
        bankName: ''
      },
      loading: false
    }
  },
  created() {
    const { name, bankCardNo, bankName } = this.$route.query
    this.params.name = name
    this.params.bankCardNo = bankCardNo
    this.params.bankName = bankName
  },
  methods: {
    onClickLeft() {
      this.$router.back()
    },
    async confirm() {
      try {
        await this.$refs.form.validate('bankCardNo')
      } catch (error) {
        return
      }
      this.loading = true
      const [err, r] = await client.apiLaborEditLaborDetail({
        body: {
          laborId: this.$route.query.laborInfoId,
          basicInfo: null,
          photos: null,
          projectHistory: null,
          laborBankCard: {
            bankName: this.params.bankName,
            bankCardNo: this.params.bankCardNo
          },
        }
      })
      this.loading = false
      if (err) return handleError(err)
      this.$toast('绑定成功')
      this.$router.push({
        path: '/personalProfile/bankInfo',
        query: {
          laborInfoId: this.$route.query.laborInfoId
        }
      })
    }
  }
}
</script>
<style scoped>
.btn {
  width: 90vw;
  margin: 100px 0 20px;
  text-align: center;
  box-sizing: border-box;
  background: #0281ff;
  color: #fff;
  border-radius: 20px;
}
</style>
