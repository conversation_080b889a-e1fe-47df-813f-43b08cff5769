<template>
  <div
    class="receipt"
    style="background: #f3f4f7; height: 100vh; overflow: auto; padding: 0 16px"
  >
    <NavBar style="margin:0 -16px" left-text="返回" left-arrow @click-left="handleBack" />
    <div
      style="
        margin: 20px 0 16px;
        font-weight: 600;
        font-size: 16px;
        color: #1e2228;
      "
    >
      {{ showQueryMonth }}
      <Icon @click="showPicker = true" name="arrow-down" />
    </div>
    <Popup v-model="showPicker" position="bottom">
      <DatetimePicker
        v-model="queryMonth"
        type="year-month"
        title="选择年月"
        :formatter="formatter"
        @confirm="onConfirm"
        @cancel="showPicker = false"
      />
    </Popup>

    <Popup
      style="background: #f7fbfd"
      v-model="show"
      position="bottom"
      :style="{ height: '35%' }"
    >
      <div style="margin: 20px">
        <Cell
          style="border-radius: 10px 10px 0 0"
          title="实发金额"
          :value="detailForm.amount"
        />
        <Cell
          title="本次应预扣预缴税额"
          :value="detailForm.currentTaxWithholding"
        />
        <Cell title="增值税额" :value="detailForm.vatAmount" />
        <Cell
          style="border-radius: 0 0 10px 10px"
          title="增值附加税额"
          :value="detailForm.additionalTaxAmount"
        />
      </div>
    </Popup>

    <div
      v-if="list.length"
      style="
        background-image: linear-gradient(180deg, #e9edff 0%, #ffffff 16%);
        border: 1px solid #ffffff;
        border-radius: 8px;
        padding: 16px 20px 0;
      "
    >
      <div style="font-weight: 600; font-size: 20px; color: #1e2228">
        {{ showQueryMonth.slice(-1) }}<span style="font-weight: 600; font-size: 12px; color: #1e2228"
          >月</span
        >
      </div>
      <div style=""></div>
      <div
        class="item-style"
        v-for="(item, index) in list"
        :key="index"
        @click="toDetail(item)"
      >
        <img
          style="width: 40px; height: 40px; margin-right: 10px"
          src="kit/assets/images/weixin/coin.png"
          alt=""
        />

        <div
          style="
            display: flex;
            padding-bottom: 16.5px;
            border-bottom: 1px solid #dfe3eb;
          "
        >
          <div style="display: flex; flex-direction: column; gap: 9px">
            <div
              style="
                font-weight: 600;
                font-size: 14px;
                color: #1e2228;
                width: 154px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >
              {{ item.contractName }}
            </div>
            <div style="font-weight: 400; font-size: 12px; color: #828b9b">
              {{ item.completeTime }}
            </div>
          </div>
          <div style="display: flex">
            <img
              style="width: 20px; height: 20px; margin-right: 4px"
              src="kit/assets/images/weixin/total.png"
              alt=""
            />
            <div
              style="
                max-width: 87px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                font-weight: 600;
                font-size: 16px;
                color: #f63939;
              "
            >
              {{ item.payableAmount }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-else
      style="
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 150px;
      "
    >
      <img width="180" src="kit/assets/images/no-data.png" alt="" />
      <div style="text-align: center; margin-top: 20px">暂无数据</div>
    </div>
  </div>
</template>
<script>
import { Icon, Popup, DatetimePicker, Cell, NavBar } from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

function getCurrentMonth() {
  const date = new Date()
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  return `${year}-${month}`
}

export default {
  components: {
    Icon,
    Popup,
    DatetimePicker,
    Cell,
    NavBar
  },
  data() {
    return {
      showPicker: false,
      show: false,
      showQueryMonth: getCurrentMonth(),
      queryMonth: getCurrentMonth(),
      list: [],
      detailForm: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    formatter(type, val) {
      if (type === 'year') {
        return val + '年'
      }
      if (type === 'month') {
        return val + '月'
      }
      return val
    },
    async getList() {
      const [err, r] = await client.apiProxyListProxy({
        body: {
          // start: 0,
          offset: 0,
          limit: 10,
          // sorts: [
          //   {
          //     field: '',
          //     direction: ''
          //   }
          // ],
          withTotal: true,
          withDisabled: true,
          withDeleted: true,
          filters: {
            corporationId: 1,
            queryMonth: this.queryMonth
          }
        }
      })
      if (err) return handleError(err)
      this.list = r.data.list
    },
    onConfirm(value) {
      this.queryMonth = this.formatDate(value)
      this.showQueryMonth = this.formatDate(value)
      this.getList()
      this.showPicker = false
    },
    formatDate(date) {
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`
    },
    toDetail(item) {
      this.detailForm = { ...item }
      this.show = true
    },
    handleBack() {
      this.$router.push('/mine')
    }
  }
}
</script>
<style scoped>
.item-style {
  display: flex;
  margin-top: 20px;
}
</style>
