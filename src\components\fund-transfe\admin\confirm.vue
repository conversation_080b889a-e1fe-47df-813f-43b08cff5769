<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :width="width"
    :closeOnClickModal="closeOnClickModal"
    v-bind="$attrs"
    custom-class="ui-custom-confirm"
  >
    <div style="padding: 32px 20px 12px">
      <h1>
        <i class="el-icon-warning"></i>
        <span>{{ title }}</span>
      </h1>
      <div class="ui-confirm-content" v-html="content"></div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button
        @click="open(false)"
        v-if="showCancelButton"
        :loading="isCancelButtonLoading"
      >
        {{ cancelButtonText }}
      </el-button>
      <el-button
        type="primary"
        :loading="isConfirmButtonLoading"
        @click="handleSaveClick"
      >
        {{ confirmButtonText }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { delay } from 'kit/helpers/delay'

function isAsyncFunction(func) {
  return func.toString().includes('async')
}

export default {
  props: {
    title: {
      type: String,
      default: '安师大收到'
    },
    content: {
      type: String,
      default: '内容'
    },
    width: {
      type: String,
      default: '400px'
    },
    type: {
      type: String,
      default: 'warning'
    },
    showCancelButton: {
      type: Boolean,
      default: true
    },
    confirmButtonText: {
      type: String,
      default: '确定'
    },
    cancelButtonText: {
      type: String,
      default: '取消'
    },
    closeOnClickModal: {
      type: Boolean,
      default: false
    },
    confirm: {
      type: [Function, null],
      default: null
    },
    cancel: {
      type: [Function, null],
      default: null
    }
  },
  data() {
    return {
      dialogVisible: true,
      isConfirmButtonLoading: false,
      isCancelButtonLoading: false
    }
  },
  watch: {
    dialogVisible(value) {
      this.$emit('visible', value)
    }
  },
  methods: {
    open(show = true) {
      this.dialogVisible = show
    },
    async handleCancelClick() {
      if (!this.Cancel) return this.open(false)
      if (!isAsyncFunction(this.cancel) && this.cancel) {
        this.cancel()
        this.open(false)
        return
      }
      this.isCancelButtonLoading = true
      try {
        await this.Cancel()
        this.open(false)
      } finally {
        await delay(300)
        this.isCancelButtonLoading = false
      }
    },
    async handleSaveClick() {
      if (!this.confirm) return this.open(false)
      if (!isAsyncFunction(this.confirm) && this.confirm) {
        this.confirm()
        this.open(false)
        return
      }
      this.isConfirmButtonLoading = true
      try {
        await this.confirm()
        this.open(false)
      } finally {
        await delay(300)
        this.isConfirmButtonLoading = false
      }
    }
  }
}
</script>

<style>
.ui-custom-confirm .el-dialog__header {
  display: none;
}
.ui-custom-confirm .el-dialog__body {
  padding: 0;
}
.ui-custom-confirm h1 {
  color: #1e2228ff;
  font-size: 16px;
  font-weight: 600;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 24px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}
.ui-custom-confirm .el-icon-warning {
  color: #ff9a01;
  font-size: 24px;
  margin-right: 16px;
}
.ui-custom-confirm .ui-confirm-content {
  padding-left: 40px;
  color: #1e2228ff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  line-height: 22px;
}
</style>
