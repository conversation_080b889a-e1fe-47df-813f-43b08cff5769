import {
  requestDefaultHeadersInterceptor,
  jsonResponseInterceptor
} from './interceptors'
import HTTPClient from './httpClient'
import Client from './client'
import { getToken } from './token'
import store from './store'

const httpClient = new HTTPClient()

const tokenInterceptor = () => {
  return function (resource, options) {
    const token = getToken()

    var supplier = ''
    var supplierDomain = ''

    const domainInfo = store.getItem('domainInfo')
    if (domainInfo) {
      supplier = domainInfo.supplierId || ''
      supplierDomain = domainInfo.domainName || ''
    }

    const headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
      supplier: supplier,
      supplierDomain: supplierDomain
    }

    options.headers = { ...headers, ...options.headers }

    return [null, resource, options]
  }
}

const gatewayInterceptor = (resource, options) => {
  var domainInfo = store.getItem('domainInfo')
  if (!domainInfo) {
    domainInfo = {
      domainName: 'linggong.olading.com'
    }
  }
  const domainName = domainInfo.domainName
  resource = `https://${domainName}${resource}`
  return [null, resource, options]
}

httpClient.attachGlobalRequestInterceptor(gatewayInterceptor)
httpClient.attachGlobalRequestInterceptor(tokenInterceptor())
httpClient.attachGlobalRequestInterceptor(requestDefaultHeadersInterceptor())

httpClient.attachGlobalResponseInterceptor(jsonResponseInterceptor())

const client = new Client(httpClient)

const makeClient = () => client

export default makeClient
