import Index from 'kit/pages/operateLabor/merchant/index.vue'
import Login from 'kit/pages/operateLabor/merchant/login.vue'
import LoginWithCaptcha from 'kit/pages/operateLabor/merchant/loginWithCaptcha.vue'
import AccountSettings from 'kit/pages/operateLabor/merchant/accountSettings.vue'

// 业务管理
import ServiceContracts from 'kit/pages/operateLabor/merchant/serviceContracts.vue'
import ServiceContractDetail from 'kit/pages/operateLabor/merchant/serviceContractDetail.vue'

// 任务管理
import TaskInfo from 'kit/pages/operateLabor/merchant/taskInfo.vue'
import TaskPersonnelDetail from 'kit/pages/operateLabor/merchant/taskPersonnelDetail.vue'

// 人员管理
import LaborInfo from 'kit/pages/operateLabor/merchant/laborInfo.vue'
import LaborInfoNew from 'kit/pages/operateLabor/merchant/laborInfoNew.vue'

// 结算管理
import SalaryCalculate from 'kit/pages/operateLabor/merchant/salaryCalculate.vue'
import SalaryCalculateNew from 'kit/pages/operateLabor/merchant/salaryCalculateNew.vue'
import SalaryCalculateDetail from 'kit/pages/operateLabor/merchant/salaryCalculateDetail.vue'
import BillingManage from 'kit/pages/operateLabor/merchant/billingManage.vue'
import BillingManageDetail from 'kit/pages/operateLabor/merchant/billingManageDetail.vue'
import BillingManageDetailSalary from 'kit/pages/operateLabor/merchant/billingManageDetailSalary.vue'
import BillingManageDetailOtherFee from 'kit/pages/operateLabor/merchant/billingManageDetailOtherFee.vue'
import BillingManageDetailManagementFee from 'kit/pages/operateLabor/merchant/billingManageDetailManagementFee.vue'
import Invoices from 'kit/pages/operateLabor/merchant/invoices.vue'
import InvoicesDetail from 'kit/pages/operateLabor/merchant/invoicesDetail.vue'

// 设置
import Roles from 'kit/pages/operateLabor/merchant/roles.vue'
import RolesNew from 'kit/pages/operateLabor/merchant/rolesNew.vue'
import MerchantUsers from 'kit/pages/operateLabor/merchant/merchantUsers.vue'
import MerchantUsersNew from 'kit/pages/operateLabor/merchant/merchantUsersNew.vue'

// 无权限页面
import NoPermission from 'kit/pages/operateLabor/merchant/noPermission.vue'

const routes = [
  {
    path: '/',
    component: Index,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/login',
    component: Login,
    meta: {
      title: '登录',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/loginWithCaptcha',
    component: LoginWithCaptcha,
    meta: {
      title: '验证码登录',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/accountSettings',
    component: AccountSettings,
    meta: {
      title: '账户管理'
    }
  },
  // 业务管理
  {
    path: '/serviceContracts',
    component: ServiceContracts,
    meta: {
      title: '合同管理',
      noNeedBack: true
    }
  },
  {
    path: '/serviceContracts/:id',
    component: ServiceContractDetail,
    meta: {
      title: '合同详情'
    }
  },
  // 任务管理
  {
    path: '/taskInfo',
    component: TaskInfo,
    meta: {
      title: '任务信息',
      noNeedBack: true
    }
  },
  {
    path: '/taskInfo/:id/personnel',
    component: TaskPersonnelDetail,
    meta: {
      title: '任务人员详情'
    }
  },
  // 人员管理
  {
    path: '/laborInfo',
    component: LaborInfo,
    meta: {
      title: '人员信息',
      noNeedBack: true
    }
  },
  {
    path: '/laborInfo/new',
    component: LaborInfoNew,
    meta: {
      title: '添加人员'
    }
  },
  {
    path: '/laborInfo/:id',
    component: LaborInfoNew,
    meta: {
      title: '人员详情'
    }
  },
  // 结算管理
  {
    path: '/salaryCalculate',
    component: SalaryCalculate,
    meta: {
      title: '薪酬计算',
      noNeedBack: true
    }
  },
  {
    path: '/salaryCalculate/new',
    component: SalaryCalculateNew,
    meta: {
      title: '新增工资表'
    }
  },
  {
    path: '/salaryCalculate/:id',
    component: SalaryCalculateDetail,
    meta: {
      title: '工资表详情'
    }
  },
  {
    path: '/billingManage',
    component: BillingManage,
    meta: {
      title: '账单管理',
      noNeedBack: true
    }
  },
  {
    path: '/billingManage/:id',
    component: BillingManageDetail,
    meta: {
      title: '账单详情'
    }
  },
  {
    path: '/billingManage/detail/salary',
    component: BillingManageDetailSalary,
    meta: {
      title: '薪酬明细'
    }
  },
  {
    path: '/billingManage/detail/otherFee',
    component: BillingManageDetailOtherFee,
    meta: {
      title: '其他费用明细'
    }
  },
  {
    path: '/billingManage/detail/managementFee',
    component: BillingManageDetailManagementFee,
    meta: {
      title: '管理费明细'
    }
  },
  {
    path: '/invoices',
    component: Invoices,
    meta: {
      title: '发票管理',
      noNeedBack: true
    }
  },
  {
    path: '/invoices/:id',
    component: InvoicesDetail,
    meta: {
      title: '发票详情'
    }
  },
  // 设置
  {
    path: '/roles',
    component: Roles,
    meta: {
      title: '角色管理',
      noNeedBack: true
    }
  },
  {
    path: '/roles/new',
    component: RolesNew,
    meta: {
      title: '新建角色'
    }
  },
  {
    path: '/roles/:id/edit',
    component: RolesNew,
    meta: {
      title: '编辑角色'
    }
  },
  {
    path: '/members',
    component: MerchantUsers,
    meta: {
      title: '用户管理',
      noNeedBack: true
    }
  },
  {
    path: '/members/new',
    component: MerchantUsersNew,
    meta: {
      title: '新建用户'
    }
  },
  {
    path: '/members/:id/edit',
    component: MerchantUsersNew,
    meta: {
      title: '编辑用户'
    }
  },
  // 无权限页面
  {
    path: '/noPermission',
    component: NoPermission,
    meta: {
      title: '无权限',
      noNavigationsAndMenus: true
    }
  }
]

export default routes