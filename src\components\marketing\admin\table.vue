<template>
  <el-table
    :data="data"
    v-bind="$attrs"
    v-on="$listeners"
    :header-cell-style="{
      fontFamily: 'PingFang SC',
      background: '#f2f4f7ff',
      fontWeight: 600,
      borderColor: '#e4e7edff',
      fontSize: '14px',
      color: '#1e2228ff'
    }"
  >
    <!-- <slot name="table-header"></slot>  -->
    <slot></slot>
  </el-table>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      required: true
    }
  }
}
</script>
